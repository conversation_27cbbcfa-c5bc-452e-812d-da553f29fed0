#!/usr/bin/env python3
"""
Advanced KOM Decompressor - Handle custom algo2/algo3 compression from Elsword/GrandChase
"""

import os
import struct
import zlib

def decrypt_xor_key(data, key):
    """Simple XOR decryption with repeating key"""
    result = bytearray()
    key_len = len(key)
    for i, byte in enumerate(data):
        result.append(byte ^ key[i % key_len])
    return bytes(result)

def try_algo2_decompression(data):
    """Try to decompress algo2 (TGA texture compression)"""
    
    print("   🔧 Trying algo2 decompression methods...")
    
    # Method 1: Check for embedded size header
    if len(data) >= 8:
        try:
            # Try reading uncompressed size from header
            uncompressed_size = struct.unpack('<I', data[:4])[0]
            compressed_data = data[4:]
            
            if 1000 < uncompressed_size < 10000000:  # Reasonable size for TGA
                try:
                    decompressed = zlib.decompress(compressed_data)
                    if len(decompressed) == uncompressed_size:
                        print(f"      ✅ Method 1 success: {len(decompressed):,} bytes")
                        return decompressed
                except:
                    pass
        except:
            pass
    
    # Method 2: Try different header skip amounts
    for skip in [0, 4, 8, 12, 16]:
        try:
            decompressed = zlib.decompress(data[skip:])
            # Check if result looks like TGA
            if len(decompressed) > 18:
                # Basic TGA validation
                if decompressed[2] in [2, 10]:  # TGA image type
                    print(f"      ✅ Method 2 success (skip {skip}): {len(decompressed):,} bytes")
                    return decompressed
        except:
            continue
    
    # Method 3: Try XOR decryption with common keys
    xor_keys = [
        b'\x6e\x0a\xcd\xeb',  # From BMS script
        b'\x07\x02\x90\xb2',  # Reverse of above
        b'\xaa\xbb\xcc\xdd',  # Common pattern
        b'\x12\x34\x56\x78',  # Common pattern
    ]
    
    for key in xor_keys:
        try:
            decrypted = decrypt_xor_key(data, key)
            decompressed = zlib.decompress(decrypted)
            if len(decompressed) > 18:
                print(f"      ✅ Method 3 success (XOR key {key.hex()}): {len(decompressed):,} bytes")
                return decompressed
        except:
            continue
    
    # Method 4: Custom algo2 format (reverse engineering)
    try:
        # Some games use custom headers
        if len(data) >= 16:
            # Try different interpretations of the header
            for offset in range(0, min(32, len(data)), 4):
                try:
                    remaining_data = data[offset:]
                    if remaining_data.startswith(b'\x78\x9c') or remaining_data.startswith(b'\x78\xda'):
                        decompressed = zlib.decompress(remaining_data)
                        print(f"      ✅ Method 4 success (offset {offset}): {len(decompressed):,} bytes")
                        return decompressed
                except:
                    continue
    except:
        pass
    
    print("      ❌ All algo2 methods failed")
    return None

def try_algo3_decompression(data):
    """Try to decompress algo3 (Lua script compression)"""
    
    print("   🔧 Trying algo3 decompression methods...")
    
    # Method 1: Standard zlib with header
    for skip in [0, 4, 8, 12, 16]:
        try:
            decompressed = zlib.decompress(data[skip:])
            # Check if result looks like Lua
            try:
                text = decompressed.decode('utf-8', errors='ignore')
                if any(keyword in text for keyword in ['function', 'local', 'end', 'if', 'then', '--']):
                    print(f"      ✅ Method 1 success (skip {skip}): Lua script detected")
                    return decompressed
            except:
                pass
        except:
            continue
    
    # Method 2: XOR + zlib
    xor_keys = [
        b'\x6e\x0a\xcd\xeb',
        b'\x07\x02\x90\xb2',
        b'\xaa\xbb\xcc\xdd',
        b'\x12\x34\x56\x78',
    ]
    
    for key in xor_keys:
        try:
            decrypted = decrypt_xor_key(data, key)
            decompressed = zlib.decompress(decrypted)
            try:
                text = decompressed.decode('utf-8', errors='ignore')
                if 'function' in text or 'local' in text:
                    print(f"      ✅ Method 2 success (XOR key {key.hex()}): Lua script")
                    return decompressed
            except:
                pass
        except:
            continue
    
    # Method 3: Custom algo3 format
    try:
        # Try interpreting as custom compressed format
        if len(data) >= 8:
            # Some formats store original size
            for i in range(0, min(16, len(data)), 4):
                try:
                    size = struct.unpack('<I', data[i:i+4])[0]
                    if 100 < size < 100000:  # Reasonable Lua script size
                        compressed_data = data[i+4:]
                        if compressed_data.startswith(b'\x78'):
                            decompressed = zlib.decompress(compressed_data)
                            if len(decompressed) == size:
                                print(f"      ✅ Method 3 success: {len(decompressed):,} bytes")
                                return decompressed
                except:
                    continue
    except:
        pass
    
    print("      ❌ All algo3 methods failed")
    return None

def decompress_file(input_path, output_path, algo_type):
    """Decompress a single file based on algorithm type"""
    
    print(f"\n🔄 Processing: {os.path.basename(input_path)}")
    
    try:
        with open(input_path, 'rb') as f:
            data = f.read()
        
        print(f"   📊 Input size: {len(data):,} bytes")
        
        decompressed = None
        
        if algo_type == 2:
            decompressed = try_algo2_decompression(data)
        elif algo_type == 3:
            decompressed = try_algo3_decompression(data)
        
        if decompressed:
            with open(output_path, 'wb') as f:
                f.write(decompressed)
            
            print(f"   ✅ Success! Output: {len(decompressed):,} bytes")
            
            # Additional validation
            if algo_type == 2 and output_path.endswith('.tga'):
                validate_tga(output_path)
            elif algo_type == 3 and output_path.endswith('.lua'):
                validate_lua(output_path)
            
            return True
        else:
            print(f"   ❌ Decompression failed")
            return False
            
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return False

def validate_tga(file_path):
    """Validate TGA file format"""
    try:
        with open(file_path, 'rb') as f:
            header = f.read(18)
        
        if len(header) >= 18:
            image_type = header[2]
            width = struct.unpack('<H', header[12:14])[0]
            height = struct.unpack('<H', header[14:16])[0]
            bpp = header[16]
            
            print(f"      📊 TGA Info: {width}x{height}, {bpp}bpp, type {image_type}")
            
            if width > 0 and height > 0 and bpp in [8, 16, 24, 32]:
                print(f"      ✅ Valid TGA file")
            else:
                print(f"      ⚠️  Unusual TGA parameters")
    except Exception as e:
        print(f"      ❌ TGA validation error: {e}")

def validate_lua(file_path):
    """Validate Lua script"""
    try:
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
        
        lua_keywords = ['function', 'local', 'end', 'if', 'then', 'else', 'return']
        found_keywords = [kw for kw in lua_keywords if kw in content]
        
        if found_keywords:
            print(f"      ✅ Valid Lua script (keywords: {', '.join(found_keywords[:3])})")
        else:
            print(f"      ⚠️  May not be a Lua script")
            
    except Exception as e:
        print(f"      ❌ Lua validation error: {e}")

def main():
    print("🔧 ADVANCED KOM DECOMPRESSOR")
    print("=" * 60)
    
    extracted_dir = "extracted"
    output_dir = "decompressed_advanced"
    
    if not os.path.exists(extracted_dir):
        print(f"❌ Extracted directory not found: {extracted_dir}")
        return
    
    os.makedirs(output_dir, exist_ok=True)
    
    success_count = 0
    total_count = 0
    
    # Process all extracted files
    for file in os.listdir(extracted_dir):
        file_path = os.path.join(extracted_dir, file)
        
        if '.algo2.' in file:
            # TGA texture file
            base_name = file.split('.algo2.')[0]
            output_path = os.path.join(output_dir, base_name)
            
            if decompress_file(file_path, output_path, 2):
                success_count += 1
            total_count += 1
            
        elif '.algo3.' in file:
            # Lua script file
            base_name = file.split('.algo3.')[0]
            output_path = os.path.join(output_dir, base_name)
            
            if decompress_file(file_path, output_path, 3):
                success_count += 1
            total_count += 1
    
    print(f"\n🎉 ADVANCED DECOMPRESSION COMPLETE!")
    print(f"✅ Successfully decompressed: {success_count}/{total_count} files")
    
    if success_count > 0:
        print(f"\n📁 Decompressed files in '{output_dir}':")
        for file in os.listdir(output_dir):
            file_path = os.path.join(output_dir, file)
            size = os.path.getsize(file_path)
            print(f"   📄 {file} ({size:,} bytes)")

if __name__ == "__main__":
    main()
