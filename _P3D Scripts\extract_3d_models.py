#!/usr/bin/env python3
"""
3D Model Data Extractor - Extract 3D model data from ROM files
"""

import os
import struct
import math
from typing import List, Tuple, Optional

class ModelData:
    """Container for extracted 3D model data"""
    def __init__(self):
        self.vertices: List[Tuple[float, float, float]] = []
        self.normals: List[Tuple[float, float, float]] = []
        self.uvs: List[Tuple[float, float]] = []
        self.faces: List[Tuple[int, int, int]] = []
        self.colors: List[Tuple[float, float, float, float]] = []
        
    def get_vertex_count(self) -> int:
        return len(self.vertices)
    
    def get_face_count(self) -> int:
        return len(self.faces)
    
    def has_normals(self) -> bool:
        return len(self.normals) > 0
    
    def has_uvs(self) -> bool:
        return len(self.uvs) > 0
    
    def has_colors(self) -> bool:
        return len(self.colors) > 0

def analyze_float_data(data: bytes, offset: int = 0) -> List[float]:
    """Extract all float values from binary data"""
    floats = []
    
    for i in range(offset, len(data) - 4, 4):
        try:
            value = struct.unpack('<f', data[i:i+4])[0]
            # Filter reasonable float values for 3D data
            if not math.isnan(value) and not math.isinf(value) and -10000 <= value <= 10000:
                floats.append(value)
        except:
            continue
    
    return floats

def extract_vertices(data: bytes) -> List[Tuple[float, float, float]]:
    """Extract vertex coordinates (groups of 3 floats)"""
    vertices = []
    floats = analyze_float_data(data)
    
    # Group floats into vertices (x, y, z)
    for i in range(0, len(floats) - 2, 3):
        x, y, z = floats[i], floats[i+1], floats[i+2]
        
        # Validate vertex coordinates (reasonable range for game models)
        if all(-1000 <= coord <= 1000 for coord in [x, y, z]):
            vertices.append((x, y, z))
    
    return vertices

def extract_normals(data: bytes) -> List[Tuple[float, float, float]]:
    """Extract normal vectors (normalized 3D vectors)"""
    normals = []
    floats = analyze_float_data(data)
    
    for i in range(0, len(floats) - 2, 3):
        x, y, z = floats[i], floats[i+1], floats[i+2]
        
        # Check if it's a normalized vector (length close to 1)
        length = math.sqrt(x*x + y*y + z*z)
        if 0.8 <= length <= 1.2:  # Allow some tolerance
            normals.append((x, y, z))
    
    return normals

def extract_uvs(data: bytes) -> List[Tuple[float, float]]:
    """Extract UV coordinates (pairs of floats in 0-1 range)"""
    uvs = []
    floats = analyze_float_data(data)
    
    for i in range(0, len(floats) - 1, 2):
        u, v = floats[i], floats[i+1]
        
        # UV coordinates are typically in 0-1 range (with some tolerance)
        if -2.0 <= u <= 2.0 and -2.0 <= v <= 2.0:
            uvs.append((u, v))
    
    return uvs

def extract_indices(data: bytes) -> List[int]:
    """Extract face indices (16-bit or 32-bit integers)"""
    indices = []
    
    # Try 16-bit indices first
    for i in range(0, len(data) - 2, 2):
        try:
            index = struct.unpack('<H', data[i:i+2])[0]
            if 0 <= index <= 65535:  # Valid 16-bit range
                indices.append(index)
        except:
            continue
    
    # If we don't have enough indices, try 32-bit
    if len(indices) < 100:
        indices = []
        for i in range(0, len(data) - 4, 4):
            try:
                index = struct.unpack('<I', data[i:i+4])[0]
                if 0 <= index <= 100000:  # Reasonable range for game models
                    indices.append(index)
            except:
                continue
    
    return indices

def create_faces_from_indices(indices: List[int], vertex_count: int) -> List[Tuple[int, int, int]]:
    """Create triangle faces from index list"""
    faces = []
    
    # Group indices into triangles
    for i in range(0, len(indices) - 2, 3):
        i1, i2, i3 = indices[i], indices[i+1], indices[i+2]
        
        # Validate indices are within vertex range
        if all(0 <= idx < vertex_count for idx in [i1, i2, i3]):
            faces.append((i1, i2, i3))
    
    return faces

def extract_model_from_file(filepath: str) -> Optional[ModelData]:
    """Extract 3D model data from a single file"""
    
    with open(filepath, 'rb') as f:
        data = f.read()
    
    model = ModelData()
    
    # Skip the "BM" header (first 2 bytes)
    data = data[2:]
    
    print(f"🔍 Analyzing {os.path.basename(filepath)} ({len(data):,} bytes)...")
    
    # Extract different types of data
    model.vertices = extract_vertices(data)
    model.normals = extract_normals(data)
    model.uvs = extract_uvs(data)
    
    # Try to extract indices and create faces
    indices = extract_indices(data)
    if indices and model.vertices:
        model.faces = create_faces_from_indices(indices, len(model.vertices))
    
    # If no faces from indices, try to create faces from sequential vertices
    if not model.faces and len(model.vertices) >= 3:
        for i in range(0, len(model.vertices) - 2, 3):
            if i + 2 < len(model.vertices):
                model.faces.append((i, i+1, i+2))
    
    # Print analysis results
    print(f"  📊 Found: {len(model.vertices)} vertices, {len(model.faces)} faces")
    if model.normals:
        print(f"  📐 Found: {len(model.normals)} normals")
    if model.uvs:
        print(f"  🗺️  Found: {len(model.uvs)} UV coordinates")
    
    # Only return if we found meaningful data
    if len(model.vertices) >= 3:
        return model
    
    return None

def save_model_as_obj(model: ModelData, output_path: str, filename: str):
    """Save 3D model as OBJ file"""
    
    obj_file = os.path.join(output_path, f"{filename}.obj")
    
    with open(obj_file, 'w') as f:
        f.write(f"# 3D Model extracted from ROM data\n")
        f.write(f"# Vertices: {model.get_vertex_count()}\n")
        f.write(f"# Faces: {model.get_face_count()}\n\n")
        
        # Write vertices
        for v in model.vertices:
            f.write(f"v {v[0]:.6f} {v[1]:.6f} {v[2]:.6f}\n")
        
        f.write("\n")
        
        # Write UV coordinates
        if model.has_uvs():
            for uv in model.uvs:
                f.write(f"vt {uv[0]:.6f} {uv[1]:.6f}\n")
            f.write("\n")
        
        # Write normals
        if model.has_normals():
            for n in model.normals:
                f.write(f"vn {n[0]:.6f} {n[1]:.6f} {n[2]:.6f}\n")
            f.write("\n")
        
        # Write faces
        f.write(f"g {filename}\n")
        f.write("s 1\n")  # Smooth shading
        
        for face in model.faces:
            f.write("f")
            for idx in face:
                vi = idx + 1  # OBJ uses 1-based indexing
                
                if model.has_uvs() and model.has_normals():
                    # Assume 1:1 mapping for now
                    if idx < len(model.uvs) and idx < len(model.normals):
                        f.write(f" {vi}/{vi}/{vi}")
                    else:
                        f.write(f" {vi}")
                elif model.has_uvs():
                    if idx < len(model.uvs):
                        f.write(f" {vi}/{vi}")
                    else:
                        f.write(f" {vi}")
                elif model.has_normals():
                    if idx < len(model.normals):
                        f.write(f" {vi}//{vi}")
                    else:
                        f.write(f" {vi}")
                else:
                    f.write(f" {vi}")
            f.write("\n")
    
    print(f"✅ Saved 3D model: {obj_file}")

def calculate_model_bounds(model: ModelData) -> Tuple[Tuple[float, float, float], Tuple[float, float, float]]:
    """Calculate bounding box of the model"""
    if not model.vertices:
        return ((0, 0, 0), (0, 0, 0))
    
    xs = [v[0] for v in model.vertices]
    ys = [v[1] for v in model.vertices]
    zs = [v[2] for v in model.vertices]
    
    min_bounds = (min(xs), min(ys), min(zs))
    max_bounds = (max(xs), max(ys), max(zs))
    
    return min_bounds, max_bounds

def extract_all_3d_models(input_dir: str, output_dir: str):
    """Extract 3D models from all model data files"""
    
    if not os.path.exists(input_dir):
        print(f"❌ Input directory not found: {input_dir}")
        return
    
    os.makedirs(output_dir, exist_ok=True)
    
    # Get all files that were identified as containing model data
    model_files = []
    
    # Read the analysis results to find model data files
    files = [f for f in os.listdir(input_dir) if f.startswith('extracted_') and f.endswith('.bmp')]
    
    print(f"🎮 3D MODEL EXTRACTOR")
    print("=" * 60)
    print(f"📁 Input directory: {input_dir}")
    print(f"📁 Output directory: {output_dir}")
    print(f"🔍 Found {len(files)} potential model files")
    print("=" * 60)
    
    extracted_count = 0
    total_vertices = 0
    total_faces = 0
    
    for filename in sorted(files):
        filepath = os.path.join(input_dir, filename)
        
        try:
            model = extract_model_from_file(filepath)
            
            if model and model.get_vertex_count() >= 3:
                # Save the model
                base_name = os.path.splitext(filename)[0]
                save_model_as_obj(model, output_dir, base_name)
                
                # Calculate bounds
                min_bounds, max_bounds = calculate_model_bounds(model)
                print(f"  📏 Bounds: ({min_bounds[0]:.2f}, {min_bounds[1]:.2f}, {min_bounds[2]:.2f}) to ({max_bounds[0]:.2f}, {max_bounds[1]:.2f}, {max_bounds[2]:.2f})")
                
                extracted_count += 1
                total_vertices += model.get_vertex_count()
                total_faces += model.get_face_count()
                print()
            else:
                print(f"  ❌ No valid 3D data found")
                print()
                
        except Exception as e:
            print(f"  ❌ Error processing {filename}: {e}")
            print()
    
    # Summary
    print("=" * 60)
    print(f"🎉 EXTRACTION COMPLETE!")
    print(f"✅ Successfully extracted: {extracted_count} 3D models")
    print(f"📊 Total vertices: {total_vertices:,}")
    print(f"📊 Total faces: {total_faces:,}")
    
    if extracted_count > 0:
        print(f"\n🚀 BLENDER IMPORT INSTRUCTIONS:")
        print("1. Open Blender")
        print("2. Delete default cube (X key)")
        print("3. File → Import → Wavefront (.obj)")
        print(f"4. Navigate to '{output_dir}' folder")
        print("5. Select the .obj files you want to import")
        print("6. Click 'Import OBJ'")
        print("\n✨ Your extracted 3D models are ready for use!")

if __name__ == "__main__":
    input_directory = "extracted_files"
    output_directory = "extracted_3d_models"
    
    extract_all_3d_models(input_directory, output_directory)
