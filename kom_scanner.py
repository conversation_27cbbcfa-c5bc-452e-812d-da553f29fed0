#!/usr/bin/env python3
"""
KOM File Scanner - Quickly scan multiple KOM files to identify which contain 3D models
"""

import os
import struct
import subprocess

def find_kom_files():
    """Find all KOM files in current directory"""
    
    kom_files = []
    for file in os.listdir('.'):
        if file.lower().endswith('.kom'):
            kom_files.append(file)
    
    return sorted(kom_files)

def quick_extract_file_list(kom_file, quickbms_exe, bms_script):
    """Quickly extract just the file list without extracting actual files"""
    
    try:
        # Use QuickBMS to list files only
        cmd = [quickbms_exe, "-l", bms_script, kom_file]
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            return result.stdout
        else:
            return None
            
    except Exception:
        return None

def analyze_kom_header(kom_file):
    """Analyze KOM file header for basic info"""
    
    try:
        with open(kom_file, 'rb') as f:
            # Read header
            header = f.read(64)
            
            if not header.startswith(b'KOG '):
                return None
            
            # Get file size
            file_size = os.path.getsize(kom_file)
            
            # Try to read file count (at offset 0x34 according to BMS script)
            f.seek(0x34)
            file_count_data = f.read(4)
            if len(file_count_data) == 4:
                file_count = struct.unpack('<I', file_count_data)[0]
            else:
                file_count = 0
            
            return {
                'size': file_size,
                'file_count': file_count,
                'header': header[:32].hex()
            }
            
    except Exception as e:
        return None

def scan_for_model_indicators(kom_file):
    """Scan KOM file for indicators of 3D model content"""
    
    model_indicators = {
        'mesh_keywords': 0,
        'texture_keywords': 0,
        'model_extensions': 0,
        'large_files': 0,
        'binary_data': 0
    }
    
    try:
        with open(kom_file, 'rb') as f:
            # Read chunks of the file to look for patterns
            chunk_size = 8192
            position = 0
            
            while position < os.path.getsize(kom_file):
                f.seek(position)
                chunk = f.read(chunk_size)
                
                if not chunk:
                    break
                
                # Convert to lowercase for searching
                chunk_lower = chunk.lower()
                
                # Look for model-related keywords
                mesh_keywords = [b'mesh', b'vertex', b'face', b'bone', b'skeleton', b'animation']
                for keyword in mesh_keywords:
                    model_indicators['mesh_keywords'] += chunk_lower.count(keyword)
                
                # Look for texture keywords
                texture_keywords = [b'texture', b'diffuse', b'normal', b'specular', b'.tga', b'.dds', b'.png']
                for keyword in texture_keywords:
                    model_indicators['texture_keywords'] += chunk_lower.count(keyword)
                
                # Look for model file extensions
                model_extensions = [b'.mesh', b'.obj', b'.fbx', b'.dae', b'.3ds', b'.mdl', b'.smd']
                for ext in model_extensions:
                    model_indicators['model_extensions'] += chunk_lower.count(ext)
                
                # Check for binary data patterns (high entropy)
                non_text_bytes = sum(1 for b in chunk if b < 32 or b > 126)
                if non_text_bytes > len(chunk) * 0.7:  # >70% binary
                    model_indicators['binary_data'] += 1
                
                position += chunk_size // 2  # Overlap chunks
                
                # Don't scan entire huge files
                if position > 1024 * 1024:  # Stop after 1MB
                    break
    
    except Exception:
        pass
    
    return model_indicators

def calculate_model_score(header_info, indicators):
    """Calculate a score indicating likelihood of containing 3D models"""
    
    score = 0
    
    if header_info:
        # Larger files more likely to contain models
        if header_info['size'] > 1024 * 1024:  # >1MB
            score += 2
        elif header_info['size'] > 100 * 1024:  # >100KB
            score += 1
        
        # More files might indicate model data
        if header_info['file_count'] > 50:
            score += 2
        elif header_info['file_count'] > 10:
            score += 1
    
    # Model-related keywords
    score += min(indicators['mesh_keywords'], 5)
    score += min(indicators['model_extensions'] * 3, 10)  # Weight extensions heavily
    score += min(indicators['texture_keywords'], 3)
    
    # Binary data indicates potential models
    if indicators['binary_data'] > 5:
        score += 2
    
    return score

def main():
    print("🔍 KOM FILE SCANNER")
    print("=" * 60)
    print("Quickly scanning KOM files for 3D model indicators...")
    
    # Find KOM files
    kom_files = find_kom_files()
    
    if not kom_files:
        print("❌ No KOM files found in current directory")
        return
    
    print(f"📁 Found {len(kom_files)} KOM files to scan")
    
    results = []
    
    for kom_file in kom_files:
        print(f"\n🔍 Scanning: {kom_file}")
        
        # Analyze header
        header_info = analyze_kom_header(kom_file)
        
        if header_info:
            print(f"   📊 Size: {header_info['size']:,} bytes, Files: {header_info['file_count']}")
        else:
            print(f"   ❌ Could not read header")
            continue
        
        # Scan for model indicators
        indicators = scan_for_model_indicators(kom_file)
        
        # Calculate score
        score = calculate_model_score(header_info, indicators)
        
        print(f"   🎯 Model Score: {score}/20")
        
        if indicators['model_extensions'] > 0:
            print(f"   ✅ Found model file extensions!")
        if indicators['mesh_keywords'] > 5:
            print(f"   ✅ Found mesh-related keywords")
        if indicators['texture_keywords'] > 5:
            print(f"   🎨 Found texture-related keywords")
        
        results.append({
            'file': kom_file,
            'score': score,
            'size': header_info['size'],
            'file_count': header_info['file_count'],
            'indicators': indicators
        })
    
    # Sort by score
    results.sort(key=lambda x: x['score'], reverse=True)
    
    print(f"\n🎉 SCAN COMPLETE!")
    print("=" * 60)
    print("📊 KOM files ranked by likelihood of containing 3D models:")
    
    for i, result in enumerate(results):
        rank_emoji = "🥇" if i == 0 else "🥈" if i == 1 else "🥉" if i == 2 else "📄"
        
        print(f"\n{rank_emoji} {result['file']}")
        print(f"   🎯 Score: {result['score']}/20")
        print(f"   📊 Size: {result['size']:,} bytes ({result['file_count']} files)")
        
        if result['indicators']['model_extensions'] > 0:
            print(f"   ✅ Model extensions found: {result['indicators']['model_extensions']}")
        if result['indicators']['mesh_keywords'] > 0:
            print(f"   🔧 Mesh keywords: {result['indicators']['mesh_keywords']}")
        if result['indicators']['texture_keywords'] > 0:
            print(f"   🎨 Texture keywords: {result['indicators']['texture_keywords']}")
    
    # Recommendations
    top_candidates = [r for r in results if r['score'] >= 5]
    
    if top_candidates:
        print(f"\n🚀 RECOMMENDED FILES TO EXTRACT:")
        for result in top_candidates[:3]:
            print(f"   📦 {result['file']} (score: {result['score']})")
        
        print(f"\n💡 Next steps:")
        print(f"   1. Extract the highest-scoring KOM files first")
        print(f"   2. Use: python kom_extractor.py")
        print(f"   3. Look for .mesh files to convert with CyberConv")
    else:
        print(f"\n⚠️  No KOM files show strong model indicators")
        print(f"💡 Try:")
        print(f"   1. Look for other KOM files in game directory")
        print(f"   2. Check for different archive formats (.pak, .dat, .bin)")
        print(f"   3. Some games store models in separate files")

if __name__ == "__main__":
    main()
