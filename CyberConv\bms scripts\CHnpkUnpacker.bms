# NetEase Cyber Hunter NPK Unpacker by Bigchillghost
# script for QuickBMS http://quickbms.aluigi.org

idstring "NXPK"

get FolderName basename
get FileNum long
goto 0xC 0 SEEK_CUR
get EntryOffset long
set NameOffset long FileNum
math NameOffset * 0x1C
math NameOffset + EntryOffset

set OutName string ""
comtype lz4
for i = 0 < FileNum
	goto EntryOffset
	get Skip long
	get Offset long
	get ZSize long
	get Size long
	goto 8 0 SEEK_CUR
	get Compression long
	savepos EntryOffset
	goto NameOffset
	get Name string
	savepos NameOffset
	string OutName p "%s\\%s" FolderName Name
	if Compression == 2
		clog OutName Offset ZSize Size
	else if ZSize == Size
		log OutName Offset ZSize
	else
		string OutName p "%s\\unknown\\%s" FolderName Name
		log OutName Offset ZSize
	endif
next i
