﻿/********************************************************************
	Created:	2020-02-20 12:03:41
	Filename: 	EXPKDecrypt.cs
	Author:		<PERSON><PERSON><PERSON><PERSON>urpose:    Expk file offset info and data segment decryption
*********************************************************************/

using System;

namespace BinaryFileReader
{
    public enum EXPKType
    {
        Moba, //决战!平安京
        SuperWar //漫威超级战争
    }

    public static class EXPKDecrypt
    {
        public static byte[] DecryptData(byte[] data, EXPKType type)
        {
            var keyData = new byte[0x100];

            if (type == EXPKType.Moba)
            {
                Array.Copy(Moba<PERSON>or<PERSON><PERSON>, 0, keyData, 0, keyData.Length);
            }
            else if (type == EXPKType.SuperWar)
            {
                Array.Copy(SuperWarXorKey, 0, keyData, 0, keyData.Length);
            }

            int keyIndex = 0;
            byte keyTmpIndex = 0;
            for (int i = 0; i < data.Length; i++)
            {
                byte tmpData = keyData[(byte)(++keyIndex)];
                keyTmpIndex += tmpData;
                keyData[(byte)keyIndex] = keyData[keyTmpIndex];
                keyData[keyTmpIndex] = tmpData;

                data[i] ^= keyData[(byte)(keyData[(byte)keyIndex] + tmpData) & 0xFF];
            }

            return data;
        }

        /// <summary>
        /// Onmyoji Arena Keys
        /// </summary>
        private static readonly byte[] MobaXorKey = new byte[256]
        {
            0x48, 0x5A, 0xC5, 0xFD, 0x8F, 0x70, 0xA6, 0xDD, 0x1C, 0x6F, 0xB8, 0x86, 0x83, 0x78, 0xB7, 0xF7,
            0xF2, 0xB4, 0x76, 0x7F, 0xAB, 0x5C, 0x40, 0x84, 0xCC, 0xF8, 0x60, 0x9C, 0x12, 0x5B, 0x80, 0x15,
            0x72, 0x9D, 0x99, 0x42, 0x92, 0x39, 0xD3, 0xBA, 0xA7, 0xC4, 0xA9, 0xC7, 0xD4, 0x47, 0xE3, 0x31,
            0x43, 0xEC, 0x20, 0xB3, 0x4C, 0x14, 0x04, 0xD8, 0xA4, 0x8D, 0x73, 0x19, 0xF3, 0xD7, 0x79, 0x36,
            0xF1, 0x2D, 0xFB, 0x68, 0xF6, 0x8E, 0xAF, 0xA0, 0xE4, 0x9B, 0x2E, 0x49, 0x53, 0xB2, 0x65, 0x3B,
            0x0A, 0x3A, 0xC8, 0x54, 0xED, 0x00, 0xB5, 0x1D, 0xEA, 0x7B, 0x24, 0x71, 0x82, 0xC9, 0x26, 0x95,
            0x56, 0x5F, 0xB1, 0x17, 0x74, 0x44, 0xBB, 0x52, 0xF4, 0x21, 0xAC, 0x96, 0x05, 0x1A, 0x10, 0x9E,
            0xD9, 0xFF, 0x64, 0xC3, 0x4A, 0x62, 0xE2, 0x50, 0x97, 0xCA, 0xA1, 0x6A, 0x27, 0xBD, 0x6D, 0x5D,
            0xF5, 0xA8, 0x32, 0x0F, 0x9F, 0x07, 0xFC, 0xCB, 0x8B, 0x4B, 0x37, 0x55, 0x0D, 0x41, 0xCE, 0xB6,
            0x3E, 0x34, 0x8A, 0x18, 0x13, 0xBC, 0x87, 0x58, 0x46, 0x28, 0x5E, 0x2B, 0xEB, 0x63, 0x23, 0xDE,
            0x30, 0x8C, 0xA5, 0x06, 0x02, 0x57, 0xDA, 0x98, 0x7A, 0x93, 0x38, 0x03, 0xE1, 0x66, 0xE7, 0xF0,
            0x35, 0xD1, 0x6B, 0xDB, 0x08, 0xE6, 0xCD, 0x59, 0x01, 0xEE, 0x7C, 0x88, 0x33, 0xD2, 0xFA, 0x25,
            0x89, 0xD0, 0x0C, 0x3D, 0xAA, 0xDC, 0xD6, 0xC6, 0xDF, 0xE0, 0x4F, 0x3F, 0x1F, 0x77, 0xA2, 0x75,
            0xB0, 0xE8, 0x94, 0xAD, 0x7D, 0x6C, 0xC2, 0x22, 0xF9, 0xBE, 0xBF, 0x0B, 0xC1, 0x1B, 0x69, 0xEF,
            0x29, 0x3C, 0xE9, 0xC0, 0x61, 0xE5, 0x6E, 0x2F, 0x9A, 0x51, 0xD5, 0x11, 0x67, 0x16, 0xCF, 0x1E,
            0xAE, 0x4E, 0x0E, 0x81, 0x45, 0x2A, 0x91, 0x90, 0xFE, 0xA3, 0x09, 0x2C, 0x85, 0x4D, 0xB9, 0x7E
        };

        /// <summary>
        ///  Marvel Super War Keys
        /// </summary>
        private static readonly byte[] SuperWarXorKey = new byte[256]
        {
            0x51, 0x6E, 0xA7, 0x56, 0xA6, 0x90, 0x78, 0x58, 0x02, 0x89, 0xB0, 0x6C, 0x05, 0xE8, 0xD3, 0xB8,
            0x20, 0x44, 0x81, 0x16, 0x3F, 0xA8, 0x4C, 0x57, 0x47, 0xD7, 0x9B, 0x1F, 0x3C, 0xBA, 0x1C, 0x23,
            0xA4, 0x0A, 0x61, 0xBF, 0x79, 0x29, 0xF0, 0x5C, 0x28, 0xD8, 0x34, 0x62, 0x52, 0xB9, 0x70, 0x7B,
            0xE0, 0xBE, 0x00, 0xAD, 0x97, 0x1E, 0x6B, 0x72, 0x30, 0x10, 0x4A, 0xB2, 0x9E, 0x8F, 0x0B, 0xAB,
            0xDB, 0x63, 0x03, 0x32, 0x49, 0xC7, 0x0E, 0xF1, 0x98, 0xE1, 0x21, 0x6F, 0xAE, 0xCF, 0xAA, 0x5F,
            0x25, 0x5D, 0x1B, 0x5E, 0xFA, 0x7C, 0x37, 0xE9, 0xB7, 0x53, 0xF8, 0xB4, 0x08, 0x13, 0x87, 0x66,
            0xCA, 0x9A, 0x24, 0xCD, 0x04, 0x75, 0x74, 0x85, 0x15, 0x14, 0xC4, 0x6A, 0x1A, 0xA2, 0xEA, 0x8A,
            0x4B, 0xEC, 0x96, 0x12, 0xC0, 0xED, 0xF6, 0x55, 0x7E, 0x11, 0xA1, 0xC9, 0x83, 0x65, 0xEB, 0xE6,
            0xCC, 0x86, 0x7F, 0xD1, 0x18, 0xBD, 0x9C, 0x9F, 0x06, 0xC6, 0x50, 0x48, 0x88, 0xDD, 0x17, 0x6D,
            0x3B, 0xE2, 0x94, 0x71, 0x2E, 0x64, 0x31, 0x35, 0x59, 0xB5, 0x99, 0x3E, 0xFB, 0xF2, 0x60, 0x54,
            0x77, 0x76, 0x8C, 0xDE, 0x40, 0xFE, 0xB3, 0xE3, 0x39, 0xEE, 0xFF, 0xB1, 0xD5, 0xC2, 0xA3, 0x27,
            0xBC, 0xF4, 0x33, 0xD9, 0xA0, 0xA9, 0xBB, 0x73, 0x4F, 0x67, 0xFC, 0x45, 0xAC, 0xE7, 0x2A, 0x07,
            0x0D, 0x7A, 0x95, 0x09, 0x2F, 0xF3, 0xD6, 0x22, 0xA5, 0xD0, 0x2B, 0x0F, 0x0C, 0xC3, 0x7D, 0xAF,
            0x5B, 0xEF, 0xE5, 0xDA, 0x42, 0x3A, 0xDF, 0xB6, 0xDC, 0x43, 0x26, 0xD2, 0x8E, 0x91, 0x38, 0xC8,
            0x69, 0x4D, 0xCB, 0x8D, 0x80, 0x41, 0x5A, 0xE4, 0x8B, 0x1D, 0x19, 0xF7, 0xFD, 0x92, 0xCE, 0x4E,
            0x01, 0xD4, 0xC5, 0xF9, 0x46, 0x36, 0x84, 0x2C, 0xF5, 0x93, 0x68, 0x3D, 0xC1, 0x2D, 0x9D, 0x82
        };
    }
}
