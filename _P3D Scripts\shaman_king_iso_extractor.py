#!/usr/bin/env python3
"""
Shaman King ISO 3D Model Extractor - Specifically for Shaman King Power of Spirit
"""

import os
import struct
import math
from typing import List, Tuple, Optional

def extract_shaman_king_models(iso_path: str, output_dir: str):
    """Extract 3D models specifically from Shaman King ISO"""
    
    print(f"🎮 SHAMAN KING 3D MODEL EXTRACTOR")
    print("=" * 60)
    print(f"📀 ISO: {os.path.basename(iso_path)}")
    
    with open(iso_path, 'rb') as f:
        f.seek(0, 2)
        file_size = f.tell()
        f.seek(0)
        
        print(f"📊 File size: {file_size:,} bytes ({file_size/1024/1024:.1f} MB)")
    
    os.makedirs(output_dir, exist_ok=True)
    
    # Shaman King is a PlayStation 2 game, so look for PS2 3D model patterns
    model_candidates = find_ps2_model_patterns(iso_path)
    
    if not model_candidates:
        print("❌ No PS2 3D model patterns found")
        return
    
    print(f"✅ Found {len(model_candidates)} potential model sections")
    
    with open(iso_path, 'rb') as f:
        for i, (offset, size, pattern_type) in enumerate(model_candidates[:20]):  # Limit to 20 models
            print(f"\n📦 Extracting Model {i+1}...")
            print(f"   Offset: 0x{offset:08X} ({offset:,})")
            print(f"   Size: {size:,} bytes")
            print(f"   Type: {pattern_type}")
            
            f.seek(offset)
            data = f.read(size)
            
            # Try to extract vertices and faces from this data
            vertices, faces = extract_ps2_geometry(data, pattern_type)
            
            if vertices and len(vertices) >= 3:
                # Center and scale
                centered_vertices = center_and_scale_vertices(vertices)
                
                # Create OBJ file
                obj_path = os.path.join(output_dir, f"shaman_king_model_{i+1:03d}.obj")
                
                with open(obj_path, 'w') as obj_f:
                    obj_f.write(f"# Shaman King 3D Model {i+1}\n")
                    obj_f.write(f"# Extracted from offset 0x{offset:08X}\n")
                    obj_f.write(f"# Pattern type: {pattern_type}\n")
                    obj_f.write(f"# Vertices: {len(centered_vertices)}, Faces: {len(faces)}\n\n")
                    
                    # Write vertices
                    for v in centered_vertices:
                        obj_f.write(f"v {v[0]:.6f} {v[1]:.6f} {v[2]:.6f}\n")
                    
                    obj_f.write("\n")
                    
                    # Write faces
                    obj_f.write(f"g ShamanKingModel{i+1}\n")
                    obj_f.write("s 1\n")
                    
                    for face in faces:
                        if all(0 <= idx < len(centered_vertices) for idx in face):
                            obj_f.write(f"f {face[0]+1} {face[1]+1} {face[2]+1}\n")
                
                print(f"   ✅ Created: {os.path.basename(obj_path)}")
                print(f"   📊 {len(centered_vertices)} vertices, {len(faces)} faces")
            else:
                print(f"   ❌ No valid geometry found")

def find_ps2_model_patterns(iso_path: str) -> List[Tuple[int, int, str]]:
    """Find PlayStation 2 3D model patterns in the ISO"""
    
    patterns = []
    
    with open(iso_path, 'rb') as f:
        file_size = os.path.getsize(iso_path)
        
        # Look for common PS2 game file structures
        chunk_size = 1024 * 1024  # 1MB chunks
        position = 0
        
        while position < file_size:
            f.seek(position)
            chunk = f.read(chunk_size)
            
            if not chunk:
                break
            
            # Look for potential model data signatures
            
            # 1. Look for sequences that might be vertex data (floats)
            for i in range(0, len(chunk) - 48, 4):
                try:
                    # Check if we have a sequence of reasonable floats
                    floats = []
                    for j in range(0, 48, 4):  # Check 12 floats (4 vertices)
                        f_val = struct.unpack('<f', chunk[i+j:i+j+4])[0]
                        if not (math.isnan(f_val) or math.isinf(f_val)) and -1000 <= f_val <= 1000:
                            floats.append(f_val)
                        else:
                            break
                    
                    if len(floats) >= 12:  # At least 4 vertices worth
                        # This looks like vertex data
                        patterns.append((position + i, 4096, "float_vertices"))
                        
                except struct.error:
                    continue
            
            # 2. Look for 16-bit vertex data (common in PS2 games)
            for i in range(0, len(chunk) - 24, 2):
                try:
                    shorts = []
                    for j in range(0, 24, 2):  # Check 12 shorts (4 vertices)
                        s_val = struct.unpack('<H', chunk[i+j:i+j+2])[0]
                        if 0 <= s_val <= 65535:
                            shorts.append(s_val)
                        else:
                            break
                    
                    if len(shorts) >= 12:
                        # Convert to see if they make sense as coordinates
                        coords = [(s - 32768) / 1000.0 for s in shorts[:12]]
                        if all(-100 <= c <= 100 for c in coords):
                            patterns.append((position + i, 2048, "short_vertices"))
                            
                except struct.error:
                    continue
            
            # 3. Look for TMD (PlayStation model format) signatures
            tmd_pos = chunk.find(b'\x41\x00\x00\x00')  # TMD header pattern
            if tmd_pos != -1:
                patterns.append((position + tmd_pos, 8192, "tmd_model"))
            
            # 4. Look for vertex count patterns
            for i in range(0, len(chunk) - 8, 4):
                try:
                    count = struct.unpack('<I', chunk[i:i+4])[0]
                    if 10 <= count <= 10000:  # Reasonable vertex count
                        # Check if followed by vertex-like data
                        next_vals = struct.unpack('<ff', chunk[i+4:i+12])
                        if all(-1000 <= v <= 1000 and not (math.isnan(v) or math.isinf(v)) for v in next_vals):
                            patterns.append((position + i + 4, count * 12, "counted_vertices"))
                except struct.error:
                    continue
            
            position += chunk_size // 2  # Overlap chunks to catch patterns at boundaries
            
            # Progress
            progress = (position / file_size) * 100
            print(f"\r🔍 Scanning... {progress:.1f}%", end='', flush=True)
    
    print(f"\r🔍 Scan complete!                    ")
    
    # Remove duplicates and sort by position
    unique_patterns = []
    for pattern in patterns:
        is_unique = True
        for existing in unique_patterns:
            if abs(pattern[0] - existing[0]) < 1000:  # Too close
                is_unique = False
                break
        if is_unique:
            unique_patterns.append(pattern)
    
    unique_patterns.sort(key=lambda x: x[0])
    return unique_patterns[:50]  # Return top 50

def extract_ps2_geometry(data: bytes, pattern_type: str) -> Tuple[List[Tuple[float, float, float]], List[Tuple[int, int, int]]]:
    """Extract vertices and faces from PS2 model data"""
    
    vertices = []
    faces = []
    
    if pattern_type == "float_vertices":
        # Extract float vertices
        for i in range(0, len(data) - 12, 12):
            try:
                x = struct.unpack('<f', data[i:i+4])[0]
                y = struct.unpack('<f', data[i+4:i+8])[0]
                z = struct.unpack('<f', data[i+8:i+12])[0]
                
                if not any(math.isnan(v) or math.isinf(v) for v in [x, y, z]) and all(-1000 <= v <= 1000 for v in [x, y, z]):
                    vertices.append((x, y, z))
                else:
                    break
            except struct.error:
                break
    
    elif pattern_type == "short_vertices":
        # Extract 16-bit vertices
        for i in range(0, len(data) - 6, 6):
            try:
                x_raw = struct.unpack('<H', data[i:i+2])[0]
                y_raw = struct.unpack('<H', data[i+2:i+4])[0]
                z_raw = struct.unpack('<H', data[i+4:i+6])[0]
                
                x = (x_raw - 32768) / 1000.0
                y = (y_raw - 32768) / 1000.0
                z = (z_raw - 32768) / 1000.0
                
                if all(-100 <= v <= 100 for v in [x, y, z]):
                    vertices.append((x, y, z))
                else:
                    break
            except struct.error:
                break
    
    elif pattern_type == "counted_vertices":
        # First 4 bytes should be vertex count
        try:
            vertex_count = struct.unpack('<I', data[:4])[0]
            if vertex_count > len(data) // 12:
                vertex_count = len(data) // 12
            
            for i in range(vertex_count):
                offset = 4 + (i * 12)
                if offset + 12 <= len(data):
                    x = struct.unpack('<f', data[offset:offset+4])[0]
                    y = struct.unpack('<f', data[offset+4:offset+8])[0]
                    z = struct.unpack('<f', data[offset+8:offset+12])[0]
                    
                    if not any(math.isnan(v) or math.isinf(v) for v in [x, y, z]):
                        vertices.append((x, y, z))
        except struct.error:
            pass
    
    elif pattern_type == "tmd_model":
        # Try to parse TMD format
        vertices = parse_tmd_vertices(data)
    
    # Generate faces if we have vertices
    if len(vertices) >= 3:
        # Try to find face indices in the data after vertices
        vertex_data_size = len(vertices) * 12 if pattern_type == "float_vertices" else len(vertices) * 6
        face_data_start = vertex_data_size
        
        # Look for face indices
        for i in range(face_data_start, min(face_data_start + 1000, len(data) - 3), 3):
            try:
                idx1, idx2, idx3 = data[i], data[i+1], data[i+2]
                if (0 <= idx1 < len(vertices) and 
                    0 <= idx2 < len(vertices) and 
                    0 <= idx3 < len(vertices) and
                    idx1 != idx2 and idx2 != idx3 and idx1 != idx3):
                    faces.append((idx1, idx2, idx3))
                    
                    if len(faces) >= 100:  # Found enough faces
                        break
            except:
                continue
        
        # If no faces found, generate simple triangulation
        if not faces:
            faces = [(i, i+1, i+2) for i in range(0, len(vertices)-2, 3)]
    
    return vertices, faces

def parse_tmd_vertices(data: bytes) -> List[Tuple[float, float, float]]:
    """Parse TMD format vertices"""
    vertices = []
    
    # TMD format is complex, try a simple approach
    # Look for vertex data after the header
    for offset in range(16, len(data) - 12, 4):
        try:
            # Try different interpretations
            x = struct.unpack('<f', data[offset:offset+4])[0]
            y = struct.unpack('<f', data[offset+4:offset+8])[0]
            z = struct.unpack('<f', data[offset+8:offset+12])[0]
            
            if not any(math.isnan(v) or math.isinf(v) for v in [x, y, z]) and all(-1000 <= v <= 1000 for v in [x, y, z]):
                vertices.append((x, y, z))
            
            if len(vertices) >= 1000:  # Limit vertices
                break
        except struct.error:
            continue
    
    return vertices

def center_and_scale_vertices(vertices: List[Tuple[float, float, float]]) -> List[Tuple[float, float, float]]:
    """Center and scale vertices for Blender"""
    
    if not vertices:
        return vertices
    
    xs = [v[0] for v in vertices]
    ys = [v[1] for v in vertices]
    zs = [v[2] for v in vertices]
    
    # Center
    center_x = (min(xs) + max(xs)) / 2
    center_y = (min(ys) + max(ys)) / 2
    center_z = (min(zs) + max(zs)) / 2
    
    # Scale
    span_x = max(xs) - min(xs)
    span_y = max(ys) - min(ys)
    span_z = max(zs) - min(zs)
    max_span = max(span_x, span_y, span_z)
    
    scale = 4.0 / max_span if max_span > 0 else 1.0
    
    centered = []
    for x, y, z in vertices:
        new_x = (x - center_x) * scale
        new_y = (y - center_y) * scale
        new_z = (z - center_z) * scale
        centered.append((new_x, new_y, new_z))
    
    return centered

def main():
    # Look for the Shaman King ISO
    iso_file = None
    for file in os.listdir('.'):
        if 'shaman king' in file.lower() and file.lower().endswith('.iso'):
            iso_file = file
            break
    
    if not iso_file:
        print("❌ Shaman King ISO file not found!")
        print("💡 Please ensure the ISO file contains 'shaman king' in the filename")
        return
    
    extract_shaman_king_models(iso_file, "shaman_king_3d_models")
    
    print(f"\n🎉 SHAMAN KING EXTRACTION COMPLETE!")
    print(f"\n🚀 BLENDER IMPORT INSTRUCTIONS:")
    print("1. Open Blender")
    print("2. Delete default cube (X key)")
    print("3. File → Import → Wavefront (.obj)")
    print("4. Navigate to 'shaman_king_3d_models' folder")
    print("5. Select shaman_king_model_*.obj files")
    print("6. Click 'Import OBJ'")
    print("\n✨ These models are extracted from the complete ISO and should have better geometry!")

if __name__ == "__main__":
    main()
