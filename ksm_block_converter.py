#!/usr/bin/env python3
"""
KSM Block-Based Converter
Parse KSM files using the discovered block structure
"""

import os
import struct

class KSMBlockParser:
    def __init__(self, file_path):
        self.file_path = file_path
        self.data = None
        self.vertices = []
        self.faces = []
        self.blocks = []
        
    def load_file(self):
        """Load the KSM file"""
        try:
            with open(self.file_path, 'rb') as f:
                self.data = f.read()
            return True
        except Exception as e:
            print(f"❌ Error loading file: {e}")
            return False
    
    def parse_blocks(self):
        """Parse the block structure"""
        print(f"🔍 Parsing blocks...")
        
        offset = 12  # Skip KSM header
        
        while offset < len(self.data) - 8:
            try:
                # Read potential block header
                block_size = struct.unpack('<I', self.data[offset:offset+4])[0]
                block_type = struct.unpack('<I', self.data[offset+4:offset+8])[0]
                
                # Validate block
                if (block_size > 0 and block_size < len(self.data) and 
                    offset + 8 + block_size <= len(self.data)):
                    
                    block_data = self.data[offset+8:offset+8+block_size]
                    
                    self.blocks.append({
                        'offset': offset,
                        'size': block_size,
                        'type': block_type,
                        'data': block_data
                    })
                    
                    offset += 8 + block_size
                else:
                    offset += 4
                    
            except struct.error:
                offset += 4
        
        print(f"   Found {len(self.blocks)} blocks")
        return len(self.blocks) > 0
    
    def extract_vertices_from_blocks(self):
        """Extract vertices from blocks that contain float data"""
        print(f"🎮 Extracting vertices from blocks...")
        
        vertex_count = 0
        
        for i, block in enumerate(self.blocks):
            # Look for blocks that might contain vertex data
            # Based on analysis, look for blocks with reasonable sizes and float patterns
            
            if block['size'] < 100:  # Skip very small blocks
                continue
                
            data = block['data']
            
            # Try to extract consecutive groups of 3 floats (vertices)
            potential_vertices = []
            
            for offset in range(0, len(data) - 12, 12):  # Step by 12 bytes (3 floats)
                try:
                    x, y, z = struct.unpack('<3f', data[offset:offset+12])
                    
                    # Check if these look like reasonable vertex coordinates
                    if (abs(x) < 10000 and abs(y) < 10000 and abs(z) < 10000 and
                        not (x == 0 and y == 0 and z == 0) and
                        not any(math.isnan(v) or math.isinf(v) for v in [x, y, z])):
                        
                        potential_vertices.append((x, y, z))
                        
                except struct.error:
                    continue
            
            # If we found a good number of vertices in this block, use them
            if len(potential_vertices) >= 3:
                print(f"   Block {i}: Found {len(potential_vertices)} vertices")
                self.vertices.extend(potential_vertices)
                vertex_count += len(potential_vertices)
                
                # Show first few vertices from this block
                for j, (x, y, z) in enumerate(potential_vertices[:3]):
                    print(f"     Vertex {j}: {x:.3f}, {y:.3f}, {z:.3f}")
        
        print(f"   Total vertices extracted: {vertex_count}")
        return vertex_count > 0
    
    def generate_faces(self):
        """Generate faces from vertices (simple triangulation)"""
        if len(self.vertices) < 3:
            return False
            
        print(f"🔺 Generating faces...")
        
        # Simple triangulation - create triangles from consecutive vertices
        for i in range(0, len(self.vertices) - 2, 3):
            if i + 2 < len(self.vertices):
                self.faces.append((i, i + 1, i + 2))
        
        print(f"   Generated {len(self.faces)} faces")
        return len(self.faces) > 0
    
    def write_obj(self, output_path):
        """Write to OBJ file"""
        try:
            with open(output_path, 'w') as f:
                f.write("# Converted from KSM format using block parser\n")
                f.write(f"# Source: {os.path.basename(self.file_path)}\n")
                f.write(f"# Blocks found: {len(self.blocks)}\n\n")
                
                # Write vertices
                for x, y, z in self.vertices:
                    f.write(f"v {x:.6f} {y:.6f} {z:.6f}\n")
                
                f.write("\n")
                
                # Write faces (OBJ uses 1-based indexing)
                for i1, i2, i3 in self.faces:
                    f.write(f"f {i1+1} {i2+1} {i3+1}\n")
            
            return True
            
        except Exception as e:
            print(f"❌ Error writing OBJ: {e}")
            return False

def convert_ksm_file(ksm_path, obj_path):
    """Convert a single KSM file using block parsing"""
    print(f"\n🔄 Converting: {os.path.basename(ksm_path)}")
    print("-" * 50)
    
    parser = KSMBlockParser(ksm_path)
    
    # Load file
    if not parser.load_file():
        return False
    
    # Parse blocks
    if not parser.parse_blocks():
        print("❌ Could not parse block structure")
        return False
    
    # Extract vertices
    if not parser.extract_vertices_from_blocks():
        print("❌ Could not extract vertices")
        return False
    
    # Generate faces
    if not parser.generate_faces():
        print("⚠️  Could not generate faces, creating point cloud")
    
    # Write OBJ
    if parser.write_obj(obj_path):
        file_size = os.path.getsize(obj_path)
        print(f"✅ Success! Created {os.path.basename(obj_path)} ({file_size:,} bytes)")
        print(f"   Vertices: {len(parser.vertices)}")
        print(f"   Faces: {len(parser.faces)}")
        print(f"   Blocks: {len(parser.blocks)}")
        return True
    
    return False

def main():
    print("🔧 KSM BLOCK-BASED CONVERTER")
    print("=" * 50)
    
    models_dir = "models_for_blender"
    output_dir = "converted_obj_models"
    
    if not os.path.exists(models_dir):
        print(f"❌ Models directory not found: {models_dir}")
        return
    
    # Create output directory
    if os.path.exists(output_dir):
        import shutil
        shutil.rmtree(output_dir)
    os.makedirs(output_dir, exist_ok=True)
    
    # Get .X files
    x_files = [f for f in os.listdir(models_dir) if f.lower().endswith('.x')]
    
    if not x_files:
        print(f"❌ No .X files found in {models_dir}")
        return
    
    print(f"📁 Found {len(x_files)} KSM files to convert")
    
    # Sort by size and try smaller files first
    x_file_sizes = []
    for x_file in x_files:
        x_path = os.path.join(models_dir, x_file)
        size = os.path.getsize(x_path)
        x_file_sizes.append((x_file, size))
    
    x_file_sizes.sort(key=lambda x: x[1])
    
    successful_conversions = 0
    failed_conversions = 0
    
    # Convert first 5 files
    for i, (x_file, size) in enumerate(x_file_sizes[:5]):
        x_path = os.path.join(models_dir, x_file)
        obj_file = os.path.splitext(x_file)[0] + '.obj'
        obj_path = os.path.join(output_dir, obj_file)
        
        print(f"\n📦 [{i+1}/5] {x_file} ({size:,} bytes)")
        
        if convert_ksm_file(x_path, obj_path):
            successful_conversions += 1
        else:
            failed_conversions += 1
    
    # Summary
    print(f"\n🎉 CONVERSION COMPLETE!")
    print("=" * 50)
    print(f"✅ Successful: {successful_conversions}")
    print(f"❌ Failed: {failed_conversions}")
    
    if successful_conversions > 0:
        print(f"\n📁 Converted files are in: {output_dir}/")
        print(f"🚀 Import these OBJ files into Blender:")
        print(f"   File → Import → Wavefront (.obj)")
        
        # List converted files
        obj_files = [f for f in os.listdir(output_dir) if f.lower().endswith('.obj')]
        for obj_file in sorted(obj_files):
            obj_path = os.path.join(output_dir, obj_file)
            size = os.path.getsize(obj_path)
            print(f"   🎮 {obj_file} ({size:,} bytes)")

if __name__ == "__main__":
    import math
    main()
