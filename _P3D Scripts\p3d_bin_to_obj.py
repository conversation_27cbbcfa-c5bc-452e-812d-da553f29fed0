import json
import struct
import os
from typing import List, Tuple, Dict, Any, Optional

class MeshData:
    """Container for extracted mesh data"""
    def __init__(self):
        self.positions: List[Tuple[float, float, float]] = []
        self.normals: List[Tuple[float, float, float]] = []
        self.texcoords: List[Tuple[float, float]] = []
        self.indices: List[int] = []
        self.faces: List[Tuple[int, int, int]] = []
        self.material_info: Dict[str, Any] = {}

    def get_vertex_count(self) -> int:
        return len(self.positions)

    def get_face_count(self) -> int:
        return len(self.faces)

    def has_normals(self) -> bool:
        return len(self.normals) > 0

    def has_texcoords(self) -> bool:
        return len(self.texcoords) > 0

def extract_json_from_bin(data: bytes) -> Dict[str, Any]:
    """Extract JSON metadata from P3D binary file using multiple strategies"""

    # Strategy 1: Try common P3D offsets
    common_offsets = [20, 16, 12, 8, 24, 28, 32]  # 20 is most common based on analysis

    for json_start in common_offsets:
        if json_start >= len(data):
            continue

        max_len = min(64000, len(data) - json_start)
        segment = data[json_start:json_start + max_len]

        # Try increasingly shorter slices until we find a valid JSON decode
        for end in range(len(segment), 0, -1):
            try:
                text = segment[:end].decode("utf-8")
                parsed = json.loads(text)
                return parsed
            except (UnicodeDecodeError, json.JSONDecodeError):
                continue

    # Strategy 2: Look for "JSON" marker and extract from after it
    json_marker = b'JSON'
    marker_pos = data.find(json_marker)
    if marker_pos >= 0:
        json_start = marker_pos + 4  # Skip "JSON" marker
        max_len = min(64000, len(data) - json_start)
        segment = data[json_start:json_start + max_len]

        for end in range(len(segment), 0, -1):
            try:
                text = segment[:end].decode("utf-8")
                parsed = json.loads(text)
                return parsed
            except (UnicodeDecodeError, json.JSONDecodeError):
                continue

    # Strategy 3: Scan for opening brace
    text_data = data.decode('utf-8', errors='ignore')
    for i, char in enumerate(text_data):
        if char == '{':
            try:
                remaining = text_data[i:]
                parsed = json.loads(remaining)
                return parsed
            except json.JSONDecodeError:
                continue

    raise Exception("Could not extract valid JSON from binary using any strategy.")

def find_buffer_start(data: bytes) -> int:
    """Find where the binary buffer data starts after JSON"""

    # Strategy 1: Look for BIN marker (most reliable for glTF binary)
    bin_marker = b'BIN\x00'
    bin_pos = data.find(bin_marker)
    if bin_pos >= 0:
        return bin_pos + 4  # Buffer starts after BIN marker

    # Strategy 2: Look for "JSON" marker and calculate from there
    json_marker = b'JSON'
    marker_pos = data.find(json_marker)
    if marker_pos >= 0:
        json_start = marker_pos + 4

        # Try to extract JSON to find its length
        max_len = min(64000, len(data) - json_start)
        segment = data[json_start:json_start + max_len]

        for end in range(len(segment), 0, -1):
            try:
                text = segment[:end].decode("utf-8")
                json.loads(text)  # Validate JSON
                # Buffer starts after JSON + padding to align to 4-byte boundary
                buffer_start = json_start + end
                # Align to 4-byte boundary
                while buffer_start % 4 != 0:
                    buffer_start += 1

                # Check if there's a BIN marker at this position
                if data[buffer_start:buffer_start+4] == b'BIN\x00':
                    return buffer_start + 4

                return buffer_start
            except (UnicodeDecodeError, json.JSONDecodeError):
                continue

    # Strategy 3: Use common P3D structure (JSON at offset 20, then buffer)
    try:
        gltf = extract_json_from_bin(data)
        json_text = json.dumps(gltf, separators=(',', ':'))
        json_length = len(json_text.encode('utf-8'))
        buffer_start = 20 + json_length
        # Align to 4-byte boundary
        while buffer_start % 4 != 0:
            buffer_start += 1

        # Check if there's a BIN marker at this position
        if data[buffer_start:buffer_start+4] == b'BIN\x00':
            return buffer_start + 4

        return buffer_start
    except:
        pass

    # Fallback: assume buffer starts at a common offset
    return 8088  # Updated fallback based on actual file analysis

def get_buffer_data(data: bytes, bufferViews: List[Dict], accessors: List[Dict], accessor_index: int, buffer_start: int) -> List[Tuple]:
    """Extract buffer data based on glTF accessor specification"""
    acc = accessors[accessor_index]
    view = bufferViews[acc['bufferView']]

    # Calculate absolute offset: buffer start + buffer view offset + accessor offset
    offset = buffer_start + view.get('byteOffset', 0) + acc.get('byteOffset', 0)
    count = acc['count']
    component_type = acc['componentType']

    # glTF component type mappings
    dtype = {
        5120: 'b',  # BYTE
        5121: 'B',  # UNSIGNED_BYTE
        5122: 'h',  # SHORT
        5123: 'H',  # UNSIGNED_SHORT
        5125: 'I',  # UNSIGNED_INT
        5126: 'f'   # FLOAT
    }[component_type]

    # glTF type mappings
    type_count = {
        'SCALAR': 1,
        'VEC2': 2,
        'VEC3': 3,
        'VEC4': 4,
        'MAT2': 4,
        'MAT3': 9,
        'MAT4': 16
    }[acc['type']]

    stride = struct.calcsize(dtype) * type_count
    raw = data[offset : offset + stride * count]

    if len(raw) < stride * count:
        raise ValueError(f"Not enough data: expected {stride * count} bytes, got {len(raw)} bytes at offset {offset}")

    return list(struct.iter_unpack(dtype * type_count, raw))

def extract_mesh_data(bin_path: str, primitive_index: int = 0) -> MeshData:
    """Extract mesh data from P3D binary file for a specific primitive"""
    with open(bin_path, "rb") as f:
        data = f.read()

    if not data.startswith(b'P3D '):
        raise ValueError("Not a P3D .bin file")

    gltf = extract_json_from_bin(data)
    accessors = gltf["accessors"]
    bufferViews = gltf["bufferViews"]

    # Find where the binary buffer data starts
    buffer_start = find_buffer_start(data)

    mesh_data = MeshData()

    # Extract mesh information
    if "meshes" not in gltf or len(gltf["meshes"]) == 0:
        raise ValueError("No meshes found in file")

    mesh = gltf["meshes"][0]

    if primitive_index >= len(mesh["primitives"]):
        raise ValueError(f"Primitive index {primitive_index} out of range (0-{len(mesh['primitives'])-1})")

    prim = mesh["primitives"][primitive_index]

    # Extract positions (required)
    if "POSITION" in prim["attributes"]:
        mesh_data.positions = get_buffer_data(data, bufferViews, accessors, prim["attributes"]["POSITION"], buffer_start)
    else:
        raise ValueError("No position data found")

    # Extract normals (optional)
    if "NORMAL" in prim["attributes"]:
        mesh_data.normals = get_buffer_data(data, bufferViews, accessors, prim["attributes"]["NORMAL"], buffer_start)

    # Extract texture coordinates (optional)
    if "TEXCOORD_0" in prim["attributes"]:
        mesh_data.texcoords = get_buffer_data(data, bufferViews, accessors, prim["attributes"]["TEXCOORD_0"], buffer_start)
    else:
        # Create placeholder UVs if missing
        mesh_data.texcoords = [(0.0, 0.0)] * len(mesh_data.positions)

    # Extract indices
    if "indices" in prim:
        mesh_data.indices = [idx[0] for idx in get_buffer_data(data, bufferViews, accessors, prim["indices"], buffer_start)]
        mesh_data.faces = [tuple(mesh_data.indices[i:i+3]) for i in range(0, len(mesh_data.indices), 3)]
    else:
        # Generate sequential indices if missing
        mesh_data.indices = list(range(len(mesh_data.positions)))
        mesh_data.faces = [tuple(mesh_data.indices[i:i+3]) for i in range(0, len(mesh_data.indices), 3)]

    # Extract material information if available
    if "materials" in gltf and "material" in prim:
        material_index = prim["material"]
        if material_index < len(gltf["materials"]):
            mesh_data.material_info = gltf["materials"][material_index]

    return mesh_data

def extract_all_mesh_data(bin_path: str) -> List[MeshData]:
    """Extract all mesh primitives from P3D binary file"""
    with open(bin_path, "rb") as f:
        data = f.read()

    if not data.startswith(b'P3D '):
        raise ValueError("Not a P3D .bin file")

    gltf = extract_json_from_bin(data)

    if "meshes" not in gltf or len(gltf["meshes"]) == 0:
        raise ValueError("No meshes found in file")

    mesh = gltf["meshes"][0]
    all_mesh_data = []

    for i in range(len(mesh["primitives"])):
        try:
            mesh_data = extract_mesh_data(bin_path, i)
            all_mesh_data.append(mesh_data)
        except Exception as e:
            print(f"Warning: Failed to extract primitive {i}: {e}")

    return all_mesh_data

def save_as_obj(mesh_data: MeshData, out_path: str, include_normals: bool = True):
    """Save mesh data as OBJ file with optional normals"""
    with open(out_path, 'w') as f:
        # Write header
        f.write("# OBJ file generated from P3D binary\n")
        f.write(f"# Vertices: {mesh_data.get_vertex_count()}\n")
        f.write(f"# Faces: {mesh_data.get_face_count()}\n\n")

        # Write vertices
        for v in mesh_data.positions:
            f.write(f"v {v[0]} {v[1]} {v[2]}\n")

        # Write texture coordinates
        if mesh_data.has_texcoords():
            for vt in mesh_data.texcoords:
                f.write(f"vt {vt[0]} {1 - vt[1]}\n")  # flip Y-axis for OBJ

        # Write normals
        if include_normals and mesh_data.has_normals():
            for vn in mesh_data.normals:
                f.write(f"vn {vn[0]} {vn[1]} {vn[2]}\n")

        f.write("\n")

        # Write faces
        for face in mesh_data.faces:
            f.write("f")
            for idx in face:
                vi = idx + 1  # OBJ uses 1-based indexing

                if mesh_data.has_texcoords() and include_normals and mesh_data.has_normals():
                    # v/vt/vn format
                    f.write(f" {vi}/{vi}/{vi}")
                elif mesh_data.has_texcoords():
                    # v/vt format
                    f.write(f" {vi}/{vi}")
                elif include_normals and mesh_data.has_normals():
                    # v//vn format
                    f.write(f" {vi}//{vi}")
                else:
                    # v format
                    f.write(f" {vi}")
            f.write("\n")

def save_combined_obj(all_mesh_data: List[MeshData], out_path: str, include_normals: bool = True):
    """Save multiple mesh primitives as a single combined OBJ file optimized for Blender"""
    base_name = os.path.splitext(out_path)[0]
    mtl_path = base_name + ".mtl"

    with open(out_path, 'w') as f:
        # Write header
        total_vertices = sum(mesh.get_vertex_count() for mesh in all_mesh_data)
        total_faces = sum(mesh.get_face_count() for mesh in all_mesh_data)

        f.write("# OBJ file generated from P3D binary (Blender-optimized)\n")
        f.write(f"# Total vertices: {total_vertices}\n")
        f.write(f"# Total faces: {total_faces}\n")
        f.write(f"# Primitives: {len(all_mesh_data)}\n")
        f.write(f"# Compatible with Blender 2.8+\n")
        f.write(f"mtllib {os.path.basename(mtl_path)}\n\n")

        # Write all vertices first
        for i, mesh_data in enumerate(all_mesh_data):
            f.write(f"# Primitive {i} vertices ({mesh_data.get_vertex_count()} vertices)\n")
            for v in mesh_data.positions:
                # Ensure proper formatting for Blender
                f.write(f"v {v[0]:.6f} {v[1]:.6f} {v[2]:.6f}\n")

        f.write("\n")

        # Write all texture coordinates
        for i, mesh_data in enumerate(all_mesh_data):
            if mesh_data.has_texcoords():
                f.write(f"# Primitive {i} texture coordinates\n")
                for vt in mesh_data.texcoords:
                    # Flip Y-axis for OBJ and clamp to valid range
                    u = max(0.0, min(1.0, vt[0]))
                    v = max(0.0, min(1.0, 1.0 - vt[1]))
                    f.write(f"vt {u:.6f} {v:.6f}\n")

        f.write("\n")

        # Write all normals (normalized for Blender)
        if include_normals:
            for i, mesh_data in enumerate(all_mesh_data):
                if mesh_data.has_normals():
                    f.write(f"# Primitive {i} normals\n")
                    for vn in mesh_data.normals:
                        # Normalize normals for Blender
                        length = (vn[0]**2 + vn[1]**2 + vn[2]**2)**0.5
                        if length > 0:
                            nx, ny, nz = vn[0]/length, vn[1]/length, vn[2]/length
                        else:
                            nx, ny, nz = 0.0, 0.0, 1.0  # Default up normal
                        f.write(f"vn {nx:.6f} {ny:.6f} {nz:.6f}\n")

        f.write("\n")

        # Write faces with proper offsets and materials
        vertex_offset = 0
        uv_offset = 0
        normal_offset = 0

        for i, mesh_data in enumerate(all_mesh_data):
            # Create clean material name
            material_name = f"Material_{i:02d}"
            if mesh_data.material_info and "name" in mesh_data.material_info:
                # Clean up material name for Blender
                raw_name = mesh_data.material_info["name"]
                material_name = clean_material_name(raw_name)

            # Write object/group and material
            f.write(f"o Primitive_{i:02d}_{material_name}\n")
            f.write(f"g Primitive_{i:02d}\n")
            f.write(f"usemtl {material_name}\n")
            f.write(f"s 1\n")  # Enable smooth shading

            for face in mesh_data.faces:
                f.write("f")
                for idx in face:
                    vi = vertex_offset + idx + 1  # OBJ uses 1-based indexing

                    if mesh_data.has_texcoords() and include_normals and mesh_data.has_normals():
                        # v/vt/vn format
                        vti = uv_offset + idx + 1
                        vni = normal_offset + idx + 1
                        f.write(f" {vi}/{vti}/{vni}")
                    elif mesh_data.has_texcoords():
                        # v/vt format
                        vti = uv_offset + idx + 1
                        f.write(f" {vi}/{vti}")
                    elif include_normals and mesh_data.has_normals():
                        # v//vn format
                        vni = normal_offset + idx + 1
                        f.write(f" {vi}//{vni}")
                    else:
                        # v format
                        f.write(f" {vi}")
                f.write("\n")

            f.write("\n")

            # Update offsets for next primitive
            vertex_offset += mesh_data.get_vertex_count()
            if mesh_data.has_texcoords():
                uv_offset += len(mesh_data.texcoords)
            if mesh_data.has_normals():
                normal_offset += len(mesh_data.normals)

    # Create MTL file for materials
    create_mtl_file(all_mesh_data, mtl_path)

def clean_material_name(raw_name: str) -> str:
    """Clean material name for Blender compatibility"""
    # Remove file extensions and clean up
    name = raw_name.replace('.tga', '').replace('.png', '').replace('.jpg', '')
    # Replace problematic characters
    name = name.replace('_mesh_', '_').replace('Mesh_', '')
    # Remove duplicate parts
    parts = name.split('_')
    unique_parts = []
    for part in parts:
        if part and part not in unique_parts:
            unique_parts.append(part)
    return '_'.join(unique_parts)[:63]  # Blender material name limit

def create_mtl_file(all_mesh_data: List[MeshData], mtl_path: str):
    """Create MTL file for Blender material import"""
    with open(mtl_path, 'w') as f:
        f.write("# MTL file generated from P3D binary\n")
        f.write("# Compatible with Blender 2.8+\n\n")

        for i, mesh_data in enumerate(all_mesh_data):
            material_name = f"Material_{i:02d}"
            texture_name = "default.png"

            if mesh_data.material_info and "name" in mesh_data.material_info:
                raw_name = mesh_data.material_info["name"]
                material_name = clean_material_name(raw_name)
                # Extract texture filename
                if '.tga' in raw_name:
                    texture_name = raw_name.split('_')[-1] if '_' in raw_name else raw_name
                    if not texture_name.endswith('.tga'):
                        texture_name += '.tga'

            # Write material definition
            f.write(f"newmtl {material_name}\n")
            f.write(f"# Primitive {i} material\n")
            f.write("Ka 0.200000 0.200000 0.200000\n")  # Ambient
            f.write("Kd 0.800000 0.800000 0.800000\n")  # Diffuse
            f.write("Ks 0.000000 0.000000 0.000000\n")  # Specular
            f.write("Ns 0.000000\n")  # Specular exponent
            f.write("d 1.000000\n")   # Dissolve (opacity)
            f.write("illum 1\n")      # Illumination model
            f.write(f"map_Kd {texture_name}\n")  # Diffuse texture
            f.write("\n")

        print(f"✅ Created material file: {mtl_path}")

def print_mesh_info(mesh_data: MeshData):
    """Print detailed information about the extracted mesh"""
    print("=== MESH INFORMATION ===")
    print(f"Vertices: {mesh_data.get_vertex_count()}")
    print(f"Faces: {mesh_data.get_face_count()}")
    print(f"Has Normals: {mesh_data.has_normals()}")
    print(f"Has Texture Coordinates: {mesh_data.has_texcoords()}")

    if mesh_data.positions:
        # Calculate bounding box
        xs = [v[0] for v in mesh_data.positions]
        ys = [v[1] for v in mesh_data.positions]
        zs = [v[2] for v in mesh_data.positions]
        print(f"Bounding Box:")
        print(f"  X: {min(xs):.3f} to {max(xs):.3f}")
        print(f"  Y: {min(ys):.3f} to {max(ys):.3f}")
        print(f"  Z: {min(zs):.3f} to {max(zs):.3f}")

    if mesh_data.material_info:
        print(f"Material Info: {mesh_data.material_info}")
    print("========================\n")

def save_separate_obj_files(all_mesh_data: List[MeshData], base_path: str, include_normals: bool = True):
    """Save each primitive as a separate OBJ file for Blender"""
    base_name = os.path.splitext(base_path)[0]

    for i, mesh_data in enumerate(all_mesh_data):
        # Create filename for this primitive
        material_name = f"primitive_{i:02d}"
        if mesh_data.material_info and "name" in mesh_data.material_info:
            raw_name = mesh_data.material_info["name"]
            material_name = clean_material_name(raw_name)

        obj_file = f"{base_name}_{material_name}.obj"
        mtl_file = f"{base_name}_{material_name}.mtl"

        # Save individual OBJ file
        with open(obj_file, 'w') as f:
            f.write(f"# OBJ file for primitive {i} (Blender-optimized)\n")
            f.write(f"# Vertices: {mesh_data.get_vertex_count()}\n")
            f.write(f"# Faces: {mesh_data.get_face_count()}\n")
            f.write(f"mtllib {os.path.basename(mtl_file)}\n\n")

            # Write vertices
            for v in mesh_data.positions:
                f.write(f"v {v[0]:.6f} {v[1]:.6f} {v[2]:.6f}\n")

            f.write("\n")

            # Write texture coordinates
            if mesh_data.has_texcoords():
                for vt in mesh_data.texcoords:
                    u = max(0.0, min(1.0, vt[0]))
                    v = max(0.0, min(1.0, 1.0 - vt[1]))
                    f.write(f"vt {u:.6f} {v:.6f}\n")

            f.write("\n")

            # Write normals
            if include_normals and mesh_data.has_normals():
                for vn in mesh_data.normals:
                    length = (vn[0]**2 + vn[1]**2 + vn[2]**2)**0.5
                    if length > 0:
                        nx, ny, nz = vn[0]/length, vn[1]/length, vn[2]/length
                    else:
                        nx, ny, nz = 0.0, 0.0, 1.0
                    f.write(f"vn {nx:.6f} {ny:.6f} {nz:.6f}\n")

            f.write("\n")

            # Write object and material
            f.write(f"o {material_name}\n")
            f.write(f"g {material_name}\n")
            f.write(f"usemtl {material_name}\n")
            f.write("s 1\n")

            # Write faces
            for face in mesh_data.faces:
                f.write("f")
                for idx in face:
                    vi = idx + 1  # OBJ uses 1-based indexing

                    if mesh_data.has_texcoords() and include_normals and mesh_data.has_normals():
                        f.write(f" {vi}/{vi}/{vi}")
                    elif mesh_data.has_texcoords():
                        f.write(f" {vi}/{vi}")
                    elif include_normals and mesh_data.has_normals():
                        f.write(f" {vi}//{vi}")
                    else:
                        f.write(f" {vi}")
                f.write("\n")

        # Create individual MTL file
        create_mtl_file([mesh_data], mtl_file)

        print(f"✅ Created: {obj_file}")

def convert_p3d_to_obj(bin_path: str, obj_path: str, include_normals: bool = True, verbose: bool = True,
                      combine_primitives: bool = True, separate_files: bool = False):
    """Convert P3D binary file to OBJ format with Blender optimization"""
    try:
        # Extract all primitives
        all_mesh_data = extract_all_mesh_data(bin_path)

        if verbose:
            print(f"Found {len(all_mesh_data)} mesh primitives")
            for i, mesh_data in enumerate(all_mesh_data):
                print(f"\n=== PRIMITIVE {i} ===")
                print_mesh_info(mesh_data)

        if separate_files:
            # Create separate OBJ files for each primitive (best for Blender)
            save_separate_obj_files(all_mesh_data, obj_path, include_normals)
            print(f"✅ Successfully created {len(all_mesh_data)} separate OBJ files")
        elif combine_primitives:
            # Combine all primitives into a single OBJ file
            save_combined_obj(all_mesh_data, obj_path, include_normals)
            print(f"✅ Successfully converted {len(all_mesh_data)} primitives to combined OBJ: {obj_path}")
        else:
            # Extract only the first primitive
            mesh_data = all_mesh_data[0] if all_mesh_data else extract_mesh_data(bin_path, 0)
            save_as_obj(mesh_data, obj_path, include_normals)
            print(f"✅ Successfully converted first primitive to OBJ: {obj_path}")
            return [mesh_data]

        return all_mesh_data

    except Exception as e:
        print(f"❌ Error converting file: {e}")
        raise

def batch_convert(input_dir: str, output_dir: str, include_normals: bool = True):
    """Convert all .bin files in a directory to OBJ format"""
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)

    bin_files = [f for f in os.listdir(input_dir) if f.endswith('.bin')]

    if not bin_files:
        print("No .bin files found in the input directory")
        return

    print(f"Found {len(bin_files)} .bin files to convert...")

    for bin_file in bin_files:
        bin_path = os.path.join(input_dir, bin_file)
        obj_file = os.path.splitext(bin_file)[0] + '.obj'
        obj_path = os.path.join(output_dir, obj_file)

        try:
            convert_p3d_to_obj(bin_path, obj_path, include_normals, verbose=False)
            print(f"✅ Converted: {bin_file} -> {obj_file}")
        except Exception as e:
            print(f"❌ Failed to convert {bin_file}: {e}")

# === BLENDER-OPTIMIZED USAGE EXAMPLES ===
if __name__ == "__main__":
    bin_file = r"77ee4tsICR.bin"

    print("🎨 P3D to Blender Mesh Converter")
    print("=" * 50)

    try:
        # Option 1: Separate files for each primitive (RECOMMENDED for Blender)
        print("\n📁 Creating separate OBJ files for each mesh part...")
        all_mesh_data = convert_p3d_to_obj(
            bin_file,
            "blender_mesh.obj",
            separate_files=True,
            include_normals=True,
            verbose=True
        )

        print(f"\n🎯 BLENDER IMPORT INSTRUCTIONS:")
        print("1. Open Blender")
        print("2. Delete default cube (X key)")
        print("3. File > Import > Wavefront (.obj)")
        print("4. Select all the generated .obj files")
        print("5. Enable 'Import Materials' in import options")
        print("6. Click 'Import OBJ'")
        print("\n✨ Each mesh part will be a separate object with materials!")

        # Option 2: Combined file (alternative)
        print(f"\n📦 Creating combined OBJ file...")
        convert_p3d_to_obj(
            bin_file,
            "combined_mesh.obj",
            combine_primitives=True,
            separate_files=False,
            include_normals=True,
            verbose=False
        )

        # Show mesh statistics
        if all_mesh_data:
            total_verts = sum(mesh.get_vertex_count() for mesh in all_mesh_data)
            total_faces = sum(mesh.get_face_count() for mesh in all_mesh_data)
            print(f"\n📊 MESH STATISTICS:")
            print(f"   Total vertices: {total_verts:,}")
            print(f"   Total faces: {total_faces:,}")
            print(f"   Mesh parts: {len(all_mesh_data)}")

            # List all parts
            print(f"\n🧩 MESH PARTS:")
            for i, mesh_data in enumerate(all_mesh_data):
                material_name = "Unknown"
                if mesh_data.material_info and "name" in mesh_data.material_info:
                    material_name = clean_material_name(mesh_data.material_info["name"])
                print(f"   {i+1}. {material_name} ({mesh_data.get_vertex_count()} verts, {mesh_data.get_face_count()} faces)")

    except FileNotFoundError:
        print("❌ Binary file not found. Please check the file path.")
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

    # Uncomment for batch conversion:
    # print("\n📂 Batch converting all .bin files...")
    # batch_convert(".", "output_meshes")
