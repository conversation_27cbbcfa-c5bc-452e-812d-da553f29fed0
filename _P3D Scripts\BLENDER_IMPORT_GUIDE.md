# BLENDER PMX IMPORT GUIDE

## Method 1: Use Blender PMX Import Plugin (Recommended)

1. **Install mmd_tools addon for Blender:**
   - Download from: https://github.com/UuuNyaa/blender_mmd_tools
   - Or install via Blender Add-ons → Install from File

2. **Import PMX files:**
   - File → Import → MikuMikuDance Model (.pmd, .pmx)
   - Select your PMX file
   - The model will import with full rigging, materials, and textures

## Method 2: Use Converted OBJ Files

1. **Import OBJ files:**
   - File → Import → Wavefront (.obj)
   - Select the converted OBJ files
   - Note: These are simplified conversions

## PMX Files Available:

### Immortal Character:
- TDA IM.pmx (Main character model)
- blacksword.pmx (Black sword weapon)
- im sword.pmx (Immortal sword weapon)

### Knight Emperor Character:
- TDA KE.pmx (Main character model)
- ke sword.pmx (Knight Emperor sword)

### Rune Master Character:
- TDA RM.pmx (Main character model)
- rm sword.pmx (Rune Master sword)

## Tips:
- PMX files contain full rigging and materials
- Texture files are in the Texture/ folders
- Use Method 1 for best results
- Models are optimized for MikuMikuDance but work great in Blender

## Credits:
- Models by: yixila
- Original game: Elsword by KOG
