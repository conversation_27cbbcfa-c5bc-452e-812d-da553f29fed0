#!/usr/bin/env python3
"""
Try the specific password "yixila"
"""

import subprocess
import os

def try_with_powershell(password):
    """Try using PowerShell with built-in compression tools"""
    
    print(f"🔑 Trying password '{password}' with PowerShell...")
    
    # Try PowerShell Expand-Archive (might work with some RAR files)
    try:
        cmd = f'powershell -Command "Expand-Archive -Path elsword.rar -DestinationPath . -Force"'
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ Extraction successful with PowerShell!")
            return True
        else:
            print(f"❌ PowerShell failed: {result.stderr}")
    except Exception as e:
        print(f"❌ PowerShell error: {e}")
    
    return False

def try_with_python_zipfile(password):
    """Try treating as ZIP file (some RAR files are actually ZIP)"""
    
    print(f"🔑 Trying password '{password}' as ZIP file...")
    
    try:
        import zipfile
        
        with zipfile.ZipFile('elsword.rar', 'r') as zip_ref:
            zip_ref.setpassword(password.encode())
            zip_ref.extractall()
        
        print("✅ Extraction successful as ZIP!")
        return True
        
    except zipfile.BadZipFile:
        print("❌ Not a ZIP file")
    except Exception as e:
        print(f"❌ ZIP extraction error: {e}")
    
    return False

def check_if_7zip_installed():
    """Check if 7-Zip is installed"""
    
    possible_paths = [
        "C:\\Program Files\\7-Zip\\7z.exe",
        "C:\\Program Files (x86)\\7-Zip\\7z.exe",
        "7z.exe"  # If in PATH
    ]
    
    for path in possible_paths:
        try:
            result = subprocess.run([path], capture_output=True)
            print(f"✅ Found 7-Zip at: {path}")
            return path
        except FileNotFoundError:
            continue
    
    return None

def try_with_7zip(password):
    """Try with 7-Zip if available"""
    
    zip_path = check_if_7zip_installed()
    
    if not zip_path:
        print("❌ 7-Zip not found. Please install from: https://www.7-zip.org/")
        return False
    
    print(f"🔑 Trying password '{password}' with 7-Zip...")
    
    try:
        cmd = [zip_path, 'x', 'elsword.rar', f'-p{password}', '-y']
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ Extraction successful with 7-Zip!")
            return True
        else:
            print(f"❌ 7-Zip failed: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ 7-Zip error: {e}")
        return False

def main():
    password = "yixila"
    
    print(f"🔐 TRYING PASSWORD: '{password}'")
    print("=" * 50)
    
    # Try different methods
    methods = [
        ("7-Zip", try_with_7zip),
        ("ZIP format", try_with_python_zipfile),
        ("PowerShell", try_with_powershell)
    ]
    
    for method_name, method_func in methods:
        print(f"\n🔧 Method: {method_name}")
        
        if method_func(password):
            print(f"\n🎉 SUCCESS! Extracted with {method_name}")
            
            # List extracted files
            print("\n📁 Extracted files:")
            for file in os.listdir('.'):
                if file.lower().endswith(('.pmx', '.obj', '.fbx', '.dae', '.txt')):
                    print(f"   ✅ {file}")
                elif os.path.isdir(file) and not file.startswith('.'):
                    print(f"   📁 {file}/")
            
            return
    
    print(f"\n❌ Could not extract with password '{password}'")
    print("\n💡 Suggestions:")
    print("   1. Install 7-Zip from: https://www.7-zip.org/")
    print("   2. Try right-clicking the RAR file → Extract")
    print("   3. Verify the password is exactly 'yixila'")

if __name__ == "__main__":
    main()
