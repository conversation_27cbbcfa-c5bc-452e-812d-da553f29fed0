"""
Blender script to automatically import P3D mesh files
Run this script inside Blender's Text Editor or via command line

Usage in Blender:
1. Open Blender
2. Go to Scripting workspace
3. Click "New" to create a new text file
4. Paste this script
5. Modify the 'mesh_directory' path below
6. Click "Run Script"
"""

import bpy
import bmesh
import os
from mathutils import Vector

def clear_scene():
    """Clear the default scene"""
    # Select all objects
    bpy.ops.object.select_all(action='SELECT')
    # Delete all objects
    bpy.ops.object.delete(use_global=False)
    
    # Clear materials
    for material in bpy.data.materials:
        bpy.data.materials.remove(material)

def import_p3d_meshes(directory_path):
    """Import all P3D mesh OBJ files from directory"""
    
    if not os.path.exists(directory_path):
        print(f"❌ Directory not found: {directory_path}")
        return
    
    # Find all OBJ files that start with "blender_mesh_"
    obj_files = [f for f in os.listdir(directory_path) 
                 if f.startswith("blender_mesh_") and f.endswith(".obj")]
    
    if not obj_files:
        print("❌ No P3D mesh files found. Make sure you've run the conversion script first.")
        return
    
    print(f"🎨 Importing {len(obj_files)} mesh parts...")
    
    imported_objects = []
    
    for obj_file in obj_files:
        obj_path = os.path.join(directory_path, obj_file)
        
        # Import OBJ file
        bpy.ops.import_scene.obj(
            filepath=obj_path,
            use_smooth_groups=True,
            use_split_objects=True,
            use_split_groups=True,
            use_groups_as_vgroups=False,
            use_image_search=True,
            split_mode='ON',
            global_clamp_size=0.0
        )
        
        # Get the imported object (should be the last selected)
        if bpy.context.selected_objects:
            obj = bpy.context.selected_objects[-1]
            imported_objects.append(obj)
            
            # Set smooth shading
            bpy.context.view_layer.objects.active = obj
            bpy.ops.object.shade_smooth()
            
            print(f"✅ Imported: {obj.name}")
    
    # Center all objects
    if imported_objects:
        # Select all imported objects
        bpy.ops.object.select_all(action='DESELECT')
        for obj in imported_objects:
            obj.select_set(True)
        
        # Set origin to geometry center
        bpy.ops.object.origin_set(type='ORIGIN_GEOMETRY', center='BOUNDS')
        
        # Optional: Join all parts into one object
        # Uncomment the next 3 lines if you want everything as one object
        # bpy.context.view_layer.objects.active = imported_objects[0]
        # bpy.ops.object.join()
        # print("🔗 Joined all parts into one object")
    
    print(f"🎉 Successfully imported {len(imported_objects)} mesh parts!")
    
    # Set viewport shading to Material Preview for better visualization
    for area in bpy.context.screen.areas:
        if area.type == 'VIEW_3D':
            for space in area.spaces:
                if space.type == 'VIEW_3D':
                    space.shading.type = 'MATERIAL_PREVIEW'
                    break

def setup_lighting():
    """Add better lighting for the imported mesh"""
    # Add a sun light
    bpy.ops.object.light_add(type='SUN', location=(5, 5, 10))
    sun = bpy.context.object
    sun.data.energy = 3.0
    sun.rotation_euler = (0.785398, 0, 0.785398)  # 45 degrees
    
    # Add fill light
    bpy.ops.object.light_add(type='AREA', location=(-5, -5, 5))
    fill = bpy.context.object
    fill.data.energy = 1.0
    fill.data.size = 10
    
    print("💡 Added lighting setup")

def main():
    """Main function to run the import process"""
    
    # ⚠️ MODIFY THIS PATH TO YOUR MESH DIRECTORY ⚠️
    mesh_directory = r"C:\Users\<USER>\OneDrive\Documents\_MyTestScripts"
    
    print("🚀 Starting P3D Mesh Import...")
    print("=" * 50)
    
    # Clear the scene
    clear_scene()
    print("🧹 Cleared scene")
    
    # Import meshes
    import_p3d_meshes(mesh_directory)
    
    # Setup lighting
    setup_lighting()
    
    # Frame all objects in view
    bpy.ops.view3d.view_all()
    
    print("=" * 50)
    print("✨ Import complete! Your P3D mesh is ready in Blender!")
    print("\n💡 Tips:")
    print("- Use Material Preview or Rendered viewport shading")
    print("- Add textures to the materials for full appearance")
    print("- Use Ctrl+A to apply transforms if needed")

# Run the script
if __name__ == "__main__":
    main()
