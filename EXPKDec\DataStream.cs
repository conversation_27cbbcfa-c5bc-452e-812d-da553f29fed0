﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Net;
using System.Text;

namespace BinaryFileReader
{
    public enum DataModel
    {
        Read,
        Write,
        ReadOrWrite,
    }

    public class DataStream : IDisposable
    {
        private FileStream _fileStream;
        private MemoryStream _memoryStream;
        private BinaryReader _reader;
        private BinaryWriter _writer;
        private Encoding _encoding = Encoding.Default;
        private bool _disposedValue = false;
        private bool _isFileStream = true;

        public long Length
        {
            get { return _isFileStream ? _fileStream.Length : _memoryStream.Length; }
        }

        public long Position
        {
            get { return _isFileStream ? _fileStream.Position : _memoryStream.Position; }
        }

        public DataStream(string file, FileMode fileModel, DataModel dataModel = DataModel.ReadOrWrite, Encoding encoding = null)
        {
            CheckDirectory(file, fileModel);
            _fileStream = new FileStream(file, fileModel);
            Init(_fileStream, dataModel, encoding, true);
        }

        public DataStream(byte[] buffer, DataModel dataModel = DataModel.ReadOrWrite, Encoding encoding = null)
        {
            _memoryStream = buffer == null ? new MemoryStream() : new MemoryStream(buffer);
            Init(_memoryStream, dataModel, encoding, false);
        }

        private void Init(Stream stream, DataModel dataModel, Encoding encoding, bool isFileStream)
        {
            _encoding = encoding ?? _encoding;
            _isFileStream = isFileStream;

            _reader = dataModel == DataModel.Read || dataModel == DataModel.ReadOrWrite ? new BinaryReader(stream, _encoding) : null;
            _writer = dataModel == DataModel.Write || dataModel == DataModel.ReadOrWrite ? new BinaryWriter(stream, _encoding) : null;
        }

        private void CheckDirectory(string file, FileMode fileModel)
        {
            var directoryName = Path.GetDirectoryName(file);
            if (fileModel == FileMode.CreateNew || fileModel == FileMode.Create || fileModel == FileMode.OpenOrCreate)
            {
                if (!Directory.Exists(directoryName))
                {
                    Directory.CreateDirectory(directoryName);
                }
            }
        }

        public long Seek(long offset, SeekOrigin seekOrigin = SeekOrigin.Begin)
        {
            return _isFileStream ? _fileStream.Seek(offset, seekOrigin) : _memoryStream.Seek(offset, seekOrigin);
        }

        public byte[] ToArray()
        {
            return _isFileStream ? ReadBytes((int)_fileStream.Length) : _memoryStream.ToArray();
        }

        public byte ReadByte()
        {
            return _reader.ReadByte();
        }

        public float ReadSingle()
        {
            return _reader.ReadSingle();
        }

        public double ReadDouble()
        {
            return _reader.ReadDouble();
        }

        public short ReadInt16(bool isBigEndian = false)
        {
            return isBigEndian ? IPAddress.HostToNetworkOrder(_reader.ReadInt16()) : _reader.ReadInt16();
        }

        public ushort ReadUInt16(bool isBigEndian = false)
        {
            return isBigEndian ? (ushort)IPAddress.HostToNetworkOrder(_reader.ReadInt16()) : _reader.ReadUInt16();
        }

        public int ReadInt32(bool isBigEndian = false)
        {
            return isBigEndian ? IPAddress.HostToNetworkOrder(_reader.ReadInt32()) : _reader.ReadInt32();
        }

        public uint ReadUInt32(bool isBigEndian = false)
        {
            return isBigEndian ? (uint)IPAddress.HostToNetworkOrder(_reader.ReadInt32()) : _reader.ReadUInt32();
        }

        public long ReadInt64(bool isBigEndian = false)
        {
            return isBigEndian ? IPAddress.HostToNetworkOrder(_reader.ReadInt64()) : _reader.ReadInt64();
        }

        public ulong ReadUInt64(bool isBigEndian = false)
        {
            return isBigEndian ? (ulong)IPAddress.HostToNetworkOrder(_reader.ReadInt64()) : _reader.ReadUInt64();
        }

        public string ReadString(int count, Encoding encoding = null)
        {
            return (encoding ?? _encoding).GetString(ReadBytes(count));
        }

        public string ReadStringToNull(Encoding encoding = null)
        {
            var bytes = new List<byte>();
            int count = 0;
            while (Position != Length && count < int.MaxValue)
            {
                var b = ReadByte();
                if (b == 0)
                {
                    break;
                }
                bytes.Add(b);
                count++;
            }
            return (encoding ?? _encoding).GetString(bytes.ToArray());
        }

        public byte[] ReadBytes()
        {
            return _reader.ReadBytes((int)(Length - Position));
        }

        public byte[] ReadBytes(int count)
        {
            return _reader.ReadBytes(count);
        }

        public byte[] ReadBytes(int offset, int count)
        {
            Seek(offset);
            return _reader.ReadBytes(count);
        }

        public void WriteByte(byte value)
        {
            _writer.Write(value);
        }

        public void WriteSingle(float value)
        {
            _writer.Write(value);
        }

        public void WriteDouble(double value)
        {
            _writer.Write(value);
        }

        public void WriteInt16(short value, bool isBigEndian = false)
        {
            _writer.Write(isBigEndian ? IPAddress.HostToNetworkOrder(value) : value);
        }

        public void WriteInt32(int value, bool isBigEndian = false)
        {
            _writer.Write(isBigEndian ? IPAddress.HostToNetworkOrder(value) : value);
        }

        public void WriteInt64(long value, bool isBigEndian = false)
        {
            _writer.Write(isBigEndian ? IPAddress.HostToNetworkOrder(value) : value);
        }

        public void WriteString(string value, Encoding encoding = null)
        {
            byte[] byteArray = (encoding ?? _encoding).GetBytes(value);

            _writer.Write(byteArray);
        }

        public void WriteBytes(byte[] buffer)
        {
            _writer.Write(buffer);
        }

        public void WriteBytes(byte[] buffer, int index, int count)
        {
            _writer.Write(buffer, index, count);
        }

        public void Write(byte[] buffer, int index, int count)
        {
            _memoryStream.Write(buffer, index, count);
        }

        public void Dispose()
        {
            Dispose(true);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (!_disposedValue)
            {
                if (disposing)
                {
                    _reader?.Close();
                    _writer?.Close();
                    _fileStream?.Dispose();
                    _memoryStream?.Dispose();
                }

                _disposedValue = true;
            }
        }
    }
}