#!/usr/bin/env python3
"""
Diagnostic script to examine P3D binary file structure
"""

import struct
import json

def examine_p3d_structure(bin_path: str):
    """Examine the detailed structure of a P3D binary file"""
    with open(bin_path, "rb") as f:
        data = f.read()
    
    print(f"File size: {len(data)} bytes")
    print(f"Header: {data[:4]}")
    
    if not data.startswith(b'P3D '):
        print("❌ Not a P3D file")
        return
    
    print("✅ P3D file detected")
    
    # Show hex dump of first 200 bytes
    print("\nHex dump (first 200 bytes):")
    for i in range(0, min(200, len(data)), 16):
        hex_part = ' '.join(f'{b:02X}' for b in data[i:i+16])
        ascii_part = ''.join(chr(b) if 32 <= b <= 126 else '.' for b in data[i:i+16])
        print(f"{i:04X}: {hex_part:<48} {ascii_part}")
    
    # Try to find JSON data at different offsets
    print("\nSearching for JSON data...")
    
    # Common P3D/glTF binary offsets to try
    offsets_to_try = [8, 12, 16, 20, 24, 28, 32]
    
    for offset in offsets_to_try:
        print(f"\nTrying offset {offset}:")
        if offset >= len(data):
            print("  Offset beyond file size")
            continue
            
        # Look for opening brace
        segment = data[offset:offset + 100]
        try:
            text = segment.decode('utf-8', errors='ignore')
            print(f"  Text preview: {repr(text[:50])}")
            
            # Look for JSON start
            brace_pos = text.find('{')
            if brace_pos >= 0:
                print(f"  Found opening brace at position {brace_pos}")
                json_start = offset + brace_pos
                
                # Try to extract JSON from this position
                success = try_extract_json_at_offset(data, json_start)
                if success:
                    return json_start
        except Exception as e:
            print(f"  Error: {e}")
    
    # Try scanning for JSON patterns
    print("\nScanning entire file for JSON patterns...")
    text_data = data.decode('utf-8', errors='ignore')
    
    # Look for common glTF JSON patterns
    patterns = ['"asset"', '"buffers"', '"bufferViews"', '"accessors"', '"meshes"']
    
    for pattern in patterns:
        pos = text_data.find(pattern)
        if pos >= 0:
            print(f"Found '{pattern}' at position {pos}")
            
            # Look backwards for opening brace
            for i in range(pos, max(0, pos - 100), -1):
                if text_data[i] == '{':
                    print(f"  Potential JSON start at {i}")
                    success = try_extract_json_at_offset(data, i)
                    if success:
                        return i
                    break
    
    return None

def try_extract_json_at_offset(data: bytes, offset: int) -> bool:
    """Try to extract JSON starting at a specific offset"""
    max_len = min(64000, len(data) - offset)
    segment = data[offset:offset + max_len]
    
    print(f"    Trying JSON extraction from offset {offset}...")
    
    # Try increasingly shorter slices
    for end in range(len(segment), 0, -1):
        try:
            text = segment[:end].decode("utf-8")
            parsed = json.loads(text)
            print(f"    ✅ SUCCESS! JSON found, length: {end}")
            print(f"    JSON keys: {list(parsed.keys())}")
            return True
        except (UnicodeDecodeError, json.JSONDecodeError):
            continue
    
    print(f"    ❌ No valid JSON found at offset {offset}")
    return False

def improved_json_extraction(data: bytes):
    """Improved JSON extraction with multiple strategies"""
    
    # Strategy 1: Try common offsets
    common_offsets = [8, 12, 16, 20, 24, 28, 32]
    
    for offset in common_offsets:
        if offset >= len(data):
            continue
            
        max_len = min(64000, len(data) - offset)
        segment = data[offset:offset + max_len]
        
        for end in range(len(segment), 0, -1):
            try:
                text = segment[:end].decode("utf-8")
                parsed = json.loads(text)
                print(f"✅ JSON found at offset {offset}, length {end}")
                return parsed, offset
            except (UnicodeDecodeError, json.JSONDecodeError):
                continue
    
    # Strategy 2: Scan for opening brace
    text_data = data.decode('utf-8', errors='ignore')
    
    for i, char in enumerate(text_data):
        if char == '{':
            try:
                # Try to parse from this position
                remaining = text_data[i:]
                parsed = json.loads(remaining)
                print(f"✅ JSON found by scanning at position {i}")
                return parsed, i
            except json.JSONDecodeError:
                continue
    
    raise Exception("Could not extract valid JSON from binary using any strategy.")

if __name__ == "__main__":
    bin_file = "34871HefLI.bin"
    
    print("🔍 P3D Binary File Diagnostic")
    print("=" * 50)
    
    try:
        json_offset = examine_p3d_structure(bin_file)
        
        if json_offset is not None:
            print(f"\n✅ JSON data found at offset: {json_offset}")
        else:
            print("\n❌ Could not locate JSON data")
            
        # Try improved extraction
        print("\n" + "=" * 50)
        print("Testing improved JSON extraction...")
        
        with open(bin_file, "rb") as f:
            data = f.read()
        
        json_data, offset = improved_json_extraction(data)
        print(f"✅ Successfully extracted JSON at offset {offset}")
        print(f"JSON structure: {json.dumps(json_data, indent=2)[:500]}...")
        
    except Exception as e:
        print(f"❌ Diagnostic failed: {e}")
        import traceback
        traceback.print_exc()
