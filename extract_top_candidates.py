#!/usr/bin/env python3
"""
Extract Top Candidate KOM Files - Extract the highest-scoring KOM files for 3D models
"""

import os
import subprocess
import shutil

def extract_kom_file(kom_path, output_dir, quickbms_exe, bms_script):
    """Extract a specific KOM file"""
    
    kom_name = os.path.basename(kom_path)
    print(f"\n🔧 EXTRACTING: {kom_name}")
    print("=" * 50)
    
    # Clean output directory
    if os.path.exists(output_dir):
        shutil.rmtree(output_dir)
    os.makedirs(output_dir, exist_ok=True)
    
    try:
        cmd = [quickbms_exe, bms_script, kom_path, output_dir]
        print(f"🔧 Running: {' '.join([os.path.basename(x) for x in cmd])}")
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            print("✅ Extraction successful!")
            
            # Analyze extracted files
            extracted_files = []
            model_files = []
            mesh_files = []
            texture_files = []
            large_files = []
            
            for root, dirs, files in os.walk(output_dir):
                for file in files:
                    file_path = os.path.join(root, file)
                    file_size = os.path.getsize(file_path)
                    file_ext = os.path.splitext(file)[1].lower()
                    
                    extracted_files.append((file, file_size, file_ext))
                    
                    # Look for model files
                    model_extensions = ['.obj', '.fbx', '.dae', '.3ds', '.mdl', '.smd', '.x', '.md2', '.md3']
                    if file_ext in model_extensions:
                        model_files.append((file, file_size))
                    
                    # Look for mesh files specifically
                    if file_ext == '.mesh' or '.mesh' in file.lower():
                        mesh_files.append((file, file_size))
                    
                    # Look for texture files
                    texture_extensions = ['.png', '.jpg', '.jpeg', '.bmp', '.tga', '.dds']
                    if file_ext in texture_extensions:
                        texture_files.append((file, file_size))
                    
                    # Track large files (potential models)
                    if file_size > 100000:  # >100KB
                        large_files.append((file, file_size, file_ext))
            
            print(f"📁 Extracted {len(extracted_files)} files")
            
            # Report findings
            if mesh_files:
                print(f"\n🎮 FOUND {len(mesh_files)} MESH FILES:")
                for file, size in sorted(mesh_files, key=lambda x: x[1], reverse=True):
                    print(f"   📦 {file} ({size:,} bytes)")
            
            if model_files:
                print(f"\n🎮 FOUND {len(model_files)} OTHER MODEL FILES:")
                for file, size in sorted(model_files, key=lambda x: x[1], reverse=True):
                    print(f"   📦 {file} ({size:,} bytes)")
            
            if texture_files:
                print(f"\n🎨 Found {len(texture_files)} texture files")
                for file, size in sorted(texture_files, key=lambda x: x[1], reverse=True)[:5]:
                    print(f"   🖼️  {file} ({size:,} bytes)")
            
            if large_files and not mesh_files and not model_files:
                print(f"\n📄 Large files (potential models):")
                for file, size, ext in sorted(large_files, key=lambda x: x[1], reverse=True)[:10]:
                    print(f"   📄 {file} ({size:,} bytes) {ext}")
            
            # Show file type summary
            file_types = {}
            for file, size, ext in extracted_files:
                if ext in file_types:
                    file_types[ext] += 1
                else:
                    file_types[ext] = 1
            
            print(f"\n📊 File types:")
            for ext, count in sorted(file_types.items(), key=lambda x: x[1], reverse=True)[:10]:
                print(f"   {ext if ext else '(no ext)'}: {count}")
            
            return len(mesh_files) + len(model_files), output_dir
        
        else:
            print(f"❌ Extraction failed:")
            if result.stderr:
                print(f"   Error: {result.stderr[:200]}")
            return 0, None
            
    except subprocess.TimeoutExpired:
        print("⏰ Extraction timed out")
        return 0, None
    except Exception as e:
        print(f"❌ Error: {e}")
        return 0, None

def main():
    print("🎯 TOP CANDIDATE KOM EXTRACTOR")
    print("=" * 60)
    print("Extracting highest-scoring KOM files for 3D models...")
    
    # Configuration
    game_data_dir = "F:\\Elsword64\\data"
    quickbms_exe = "QuickBMS/quickbms.exe"
    bms_script = "kom.bms"
    
    # Top candidate files (from scanner results)
    top_candidates = [
        "data006.kom",
        "data007.kom", 
        "data009.kom"
    ]
    
    # Verify tools exist
    if not os.path.exists(quickbms_exe):
        print(f"❌ QuickBMS not found: {quickbms_exe}")
        return
    
    if not os.path.exists(bms_script):
        print(f"❌ BMS script not found: {bms_script}")
        return
    
    if not os.path.exists(game_data_dir):
        print(f"❌ Game data directory not found: {game_data_dir}")
        return
    
    print(f"✅ Tools ready")
    print(f"📁 Game data: {game_data_dir}")
    
    # Extract each candidate
    results = []
    
    for kom_file in top_candidates:
        kom_path = os.path.join(game_data_dir, kom_file)
        
        if not os.path.exists(kom_path):
            print(f"\n❌ KOM file not found: {kom_file}")
            continue
        
        output_dir = f"extracted_{kom_file.replace('.kom', '')}"
        model_count, extracted_dir = extract_kom_file(kom_path, output_dir, quickbms_exe, bms_script)
        
        results.append({
            'file': kom_file,
            'model_count': model_count,
            'output_dir': extracted_dir
        })
    
    # Summary
    print(f"\n🎉 EXTRACTION COMPLETE!")
    print("=" * 60)
    
    successful_extractions = [r for r in results if r['model_count'] > 0]
    
    if successful_extractions:
        print(f"✅ Found 3D models in:")
        for result in sorted(successful_extractions, key=lambda x: x['model_count'], reverse=True):
            print(f"   🎮 {result['file']}: {result['model_count']} models in {result['output_dir']}")
        
        print(f"\n🚀 Next steps:")
        print(f"   1. Check the extracted directories for .mesh files")
        print(f"   2. Run: python mesh_to_fbx_converter.py")
        print(f"   3. Import FBX files into Blender")
        
        # Check for mesh files specifically
        total_mesh_files = 0
        for result in results:
            if result['output_dir'] and os.path.exists(result['output_dir']):
                for root, dirs, files in os.walk(result['output_dir']):
                    for file in files:
                        if file.lower().endswith('.mesh') or '.mesh' in file.lower():
                            total_mesh_files += 1
        
        if total_mesh_files > 0:
            print(f"\n🔧 Found {total_mesh_files} .mesh files total!")
            print(f"   Use CyberConv to convert them to FBX format")
        
    else:
        print(f"❌ No 3D models found in top candidates")
        print(f"\n💡 Try:")
        print(f"   1. Check extracted directories manually")
        print(f"   2. Look for large binary files that might be models")
        print(f"   3. Try other high-scoring KOM files")
        
        # Show what was extracted
        print(f"\n📊 What was extracted:")
        for result in results:
            if result['output_dir'] and os.path.exists(result['output_dir']):
                file_count = len([f for f in os.listdir(result['output_dir']) if os.path.isfile(os.path.join(result['output_dir'], f))])
                print(f"   📄 {result['file']}: {file_count} files in {result['output_dir']}")

if __name__ == "__main__":
    main()
