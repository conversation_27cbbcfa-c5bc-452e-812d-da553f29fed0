#!/usr/bin/env python3
"""
Convert DirectX .X files to FBX using CyberConv
"""

import os
import subprocess
import shutil

def convert_x_file(x_file_path, output_dir):
    """Convert a single .X file to FBX using CyberConv"""
    
    x_filename = os.path.basename(x_file_path)
    x_name = os.path.splitext(x_filename)[0]
    fbx_filename = f"{x_name}.fbx"
    fbx_path = os.path.join(output_dir, fbx_filename)
    
    print(f"\n🔄 Converting: {x_filename}")
    print("-" * 50)
    
    # Try different CyberConv command formats
    cyberconv_exe = "CyberConv/CyberConv.exe"
    
    if not os.path.exists(cyberconv_exe):
        print(f"❌ CyberConv not found: {cyberconv_exe}")
        return False
    
    # Method 1: Direct conversion
    try:
        cmd = [cyberconv_exe, x_file_path, fbx_path]
        print(f"🔧 Trying: {' '.join([os.path.basename(x) for x in cmd])}")
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)
        
        if result.returncode == 0 and os.path.exists(fbx_path):
            file_size = os.path.getsize(fbx_path)
            print(f"✅ Success! Created {fbx_filename} ({file_size:,} bytes)")
            return True
        else:
            print(f"❌ Method 1 failed")
            if result.stderr:
                print(f"   Error: {result.stderr[:100]}")
    except Exception as e:
        print(f"❌ Method 1 error: {e}")
    
    # Method 2: With format specification
    try:
        cmd = [cyberconv_exe, "-i", "x", "-o", "fbx", x_file_path, fbx_path]
        print(f"🔧 Trying: {' '.join([os.path.basename(x) if '/' not in x else x for x in cmd])}")
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)
        
        if result.returncode == 0 and os.path.exists(fbx_path):
            file_size = os.path.getsize(fbx_path)
            print(f"✅ Success! Created {fbx_filename} ({file_size:,} bytes)")
            return True
        else:
            print(f"❌ Method 2 failed")
    except Exception as e:
        print(f"❌ Method 2 error: {e}")
    
    # Method 3: Try OBJ format instead
    try:
        obj_filename = f"{x_name}.obj"
        obj_path = os.path.join(output_dir, obj_filename)
        
        cmd = [cyberconv_exe, x_file_path, obj_path]
        print(f"🔧 Trying OBJ: {' '.join([os.path.basename(x) for x in cmd])}")
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)
        
        if result.returncode == 0 and os.path.exists(obj_path):
            file_size = os.path.getsize(obj_path)
            print(f"✅ Success! Created {obj_filename} ({file_size:,} bytes)")
            return True
        else:
            print(f"❌ Method 3 failed")
    except Exception as e:
        print(f"❌ Method 3 error: {e}")
    
    print(f"❌ All conversion methods failed for {x_filename}")
    return False

def main():
    print("🔄 DIRECTX TO FBX CONVERTER")
    print("=" * 60)
    
    models_dir = "models_for_blender"
    output_dir = "converted_models"
    
    if not os.path.exists(models_dir):
        print(f"❌ Models directory not found: {models_dir}")
        return
    
    # Create output directory
    if os.path.exists(output_dir):
        shutil.rmtree(output_dir)
    os.makedirs(output_dir, exist_ok=True)
    
    # Get all .X files
    x_files = [f for f in os.listdir(models_dir) if f.lower().endswith('.x')]
    
    if not x_files:
        print(f"❌ No .X files found in {models_dir}")
        return
    
    print(f"📁 Found {len(x_files)} .X files to convert")
    
    # Convert top 10 largest files first
    x_file_sizes = []
    for x_file in x_files:
        x_path = os.path.join(models_dir, x_file)
        size = os.path.getsize(x_path)
        x_file_sizes.append((x_file, size))
    
    x_file_sizes.sort(key=lambda x: x[1], reverse=True)
    
    # Convert files
    successful_conversions = 0
    failed_conversions = 0
    
    # Try top 10 first
    for i, (x_file, size) in enumerate(x_file_sizes[:10]):
        x_path = os.path.join(models_dir, x_file)
        
        print(f"\n📦 [{i+1}/10] {x_file} ({size:,} bytes)")
        
        if convert_x_file(x_path, output_dir):
            successful_conversions += 1
        else:
            failed_conversions += 1
    
    # Summary
    print(f"\n🎉 CONVERSION COMPLETE!")
    print("=" * 60)
    print(f"✅ Successful: {successful_conversions}")
    print(f"❌ Failed: {failed_conversions}")
    
    if successful_conversions > 0:
        print(f"\n📁 Converted files are in: {output_dir}/")
        print(f"🚀 Import these into Blender:")
        
        converted_files = [f for f in os.listdir(output_dir) if f.lower().endswith(('.fbx', '.obj'))]
        for file in sorted(converted_files)[:5]:
            print(f"   🎮 {file}")
        
        print(f"\n💡 Blender import:")
        print(f"   File → Import → FBX (.fbx) or Wavefront (.obj)")
    else:
        print(f"\n❌ No files were converted successfully")
        print(f"💡 Try manual import in Blender:")
        print(f"   1. File → Import → DirectX (.x)")
        print(f"   2. If that doesn't work, the .X files might need a different converter")

if __name__ == "__main__":
    main()
