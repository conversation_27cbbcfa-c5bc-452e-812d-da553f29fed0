# Converted from KSM format using block parser
# Source: Mesh_Bboru_Thief_Basic_Weapon.X
# Blocks found: 9

v 0.000000 0.000000 0.000000
v 0.000000 0.000000 0.000000
v 0.000000 0.000000 0.000000
v 0.000000 0.023438 0.523438
v 0.226562 1.000000 0.000000
v 0.000000 0.000000 1.000000
v 0.023438 0.523438 0.226562
v 1.000000 0.000000 0.000000
v 0.000000 1.000000 0.000000
v 0.000000 -20.000000 -20.000000
v -20.000000 0.000000 -1.000000
v 0.000000 1.000000 1.000000
v 20.000000 -20.000000 -20.000000
v 0.000000 -1.000000 0.000000
v 0.000000 1.000000 -20.000000
v -20.000000 20.000000 0.000000
v -1.000000 0.000000 1.000000
v 0.000000 20.000000 -20.000000
v 20.000000 0.000000 -1.000000
v -20.000000 20.000000 -20.000000
v 0.000000 1.000000 0.000000
v 0.000000 1.000000 20.000000
v 20.000000 -20.000000 0.000000
v 1.000000 -0.000000 1.000000
v 1.000000 -20.000000 20.000000
v 20.000000 -0.000000 1.000000
v 20.000000 20.000000 20.000000
v 0.000000 1.000000 0.000000
v 1.000000 0.000000 -20.000000
v -20.000000 -20.000000 0.000000
v 0.000000 -1.000000 0.000000
v 1.000000 20.000000 -20.000000
v -20.000000 0.000000 0.000000
v -1.000000 1.000000 1.000000
v 20.000000 20.000000 -20.000000
v 0.000000 0.000000 -1.000000
v 1.000000 0.000000 -20.000000
v 20.000000 -20.000000 0.000000
v 0.000000 -1.000000 0.000000
v 0.000000 20.000000 -20.000000
v -20.000000 1.000000 0.000000
v 0.000000 0.000000 1.000000
v 20.000000 -20.000000 20.000000
v 1.000000 -0.000000 0.000000
v 1.000000 1.000000 20.000000
v 20.000000 20.000000 1.000000
v 0.000000 0.000000 1.000000
v 0.000000 20.000000 20.000000
v -20.000000 1.000000 0.000000
v 20.000000 -20.000000 20.000000
v 0.000000 0.000000 1.000000
v 0.000000 1.000000 -20.000000
v -20.000000 20.000000 0.000000
v 0.000000 1.000000 1.000000
v 1.000000 -20.000000 20.000000
v 20.000000 0.000000 0.000000
v 1.000000 1.000000 0.000000
v 20.000000 20.000000 20.000000
v 0.000000 0.000000 1.000000
v 0.000000 0.000000 -20.000000
v -20.000000 20.000000 -1.000000
v 1.000000 -20.000000 -20.000000
v -20.000000 -1.000000 0.000000
v 0.000000 1.000000 1.000000
v -20.000000 20.000000 -20.000000
v -1.000000 0.000000 0.000000
v 1.000000 0.000000 -20.000000
v 20.000000 20.000000 -1.000000
v 0.000000 0.000000 0.000000
v 0.000000 0.000000 0.000000
v 0.000000 0.000000 0.000000
v 0.000000 0.000000 0.000000
v 0.000000 0.000000 0.000000
v 0.000000 0.000000 0.000000
v 0.000000 0.000000 0.000000
v 0.000000 0.000000 0.000000
v 0.000000 0.000000 0.000000
v 0.000000 0.000000 0.000000
v 0.000000 0.691406 0.109375
v 0.582031 1.000000 0.000000
v 0.000000 0.000000 1.000000
v 0.691406 0.109375 0.582031
v 1.000000 0.000000 0.000000
v 0.000000 1.000000 0.000000
v 0.000000 -20.000000 -20.000000
v -20.000000 0.000000 -1.000000
v 0.000000 1.000000 1.000000
v 20.000000 -20.000000 -20.000000
v 0.000000 -1.000000 0.000000
v 0.000000 1.000000 -20.000000
v -20.000000 20.000000 0.000000
v -1.000000 0.000000 1.000000
v 0.000000 20.000000 -20.000000
v 20.000000 0.000000 -1.000000
v -20.000000 20.000000 -20.000000
v 0.000000 1.000000 0.000000
v 0.000000 1.000000 20.000000
v 20.000000 -20.000000 0.000000
v 1.000000 -0.000000 1.000000
v 1.000000 -20.000000 20.000000
v 20.000000 -0.000000 1.000000
v 20.000000 20.000000 20.000000
v 0.000000 1.000000 0.000000
v 1.000000 0.000000 -20.000000
v -20.000000 -20.000000 0.000000
v 0.000000 -1.000000 0.000000
v 1.000000 20.000000 -20.000000
v -20.000000 0.000000 0.000000
v -1.000000 1.000000 1.000000
v 20.000000 20.000000 -20.000000
v 0.000000 0.000000 -1.000000
v 1.000000 0.000000 -20.000000
v 20.000000 -20.000000 0.000000
v 0.000000 -1.000000 0.000000
v 0.000000 20.000000 -20.000000
v -20.000000 1.000000 0.000000
v 0.000000 0.000000 1.000000
v 20.000000 -20.000000 20.000000
v 1.000000 -0.000000 0.000000
v 1.000000 1.000000 20.000000
v 20.000000 20.000000 1.000000
v 0.000000 0.000000 1.000000
v 0.000000 20.000000 20.000000
v -20.000000 1.000000 0.000000
v 20.000000 -20.000000 20.000000
v 0.000000 0.000000 1.000000
v 0.000000 1.000000 -20.000000
v -20.000000 20.000000 0.000000
v 0.000000 1.000000 1.000000
v 1.000000 -20.000000 20.000000
v 20.000000 0.000000 0.000000
v 1.000000 1.000000 0.000000
v 20.000000 20.000000 20.000000
v 0.000000 0.000000 1.000000
v 0.000000 0.000000 -20.000000
v -20.000000 20.000000 -1.000000
v 1.000000 -20.000000 -20.000000
v -20.000000 -1.000000 0.000000
v 0.000000 1.000000 1.000000
v -20.000000 20.000000 -20.000000
v -1.000000 0.000000 0.000000
v 1.000000 0.000000 -20.000000
v 20.000000 20.000000 -1.000000
v 0.000000 0.000000 0.000000
v 0.000000 0.000000 0.000000
v 0.000000 0.000000 0.000000
v 0.000000 0.000000 0.000000
v 0.000000 0.000000 0.000000
v 0.000000 0.000000 0.000000
v 0.000000 0.000000 0.000000
v 0.000000 0.000000 193.321594
v 0.931388 0.000000 0.000000
v 0.000000 128.000000 0.000000
v 0.000000 0.000000 0.000000
v 0.000000 0.000000 0.000000
v 0.000000 0.000000 0.000000
v 0.000000 0.000000 0.000000
v 0.000000 0.000000 0.000000
v 0.000000 0.000000 0.000000
v 0.000000 0.000000 -0.000000
v -0.000000 0.000000 -0.000000
v -0.283464 -0.003082 -0.000000
v 0.000000 -0.000000 0.000000
v -0.003082 0.000000 -0.000000
v 0.000000 -0.003082 0.000001
v 0.000000 -0.000000 0.000000
v -0.003222 -0.000001 0.108345
v 0.000000 -0.000001 0.108338
v 0.000000 0.000000 -0.000000
v 0.000000 -0.000000 -0.000000
v 0.000000 -0.000000 0.000000
v 0.000000 -0.000000 -0.000000
v 0.000000 -0.000000 0.000000
v -0.000000 0.000000 0.000000
v 0.000000 -0.000000 -0.000000
v 0.000000 0.000000 0.000000
v 0.000000 5.585938 0.000000
v 0.000000 0.000000 0.000000
v 0.000000 0.000000 0.000000
v 0.000000 0.000000 0.000000
v 0.000000 0.000000 0.000000
v 0.000000 -0.000000 -0.000000
v 0.000000 0.000000 0.000000
v 0.000000 0.000000 0.000000
v 1.035883 39.044823 -5.823417
v -0.707107 -0.000000 -0.000000
v 0.707107 0.000000 0.000000
v 0.000000 0.000000 0.000000
v 0.000000 0.000000 0.000000
v 0.000000 0.000000 1.035883
v 39.087692 -5.823417 -0.707107

f 1 2 3
f 4 5 6
f 7 8 9
f 10 11 12
f 13 14 15
f 16 17 18
f 19 20 21
f 22 23 24
f 25 26 27
f 28 29 30
f 31 32 33
f 34 35 36
f 37 38 39
f 40 41 42
f 43 44 45
f 46 47 48
f 49 50 51
f 52 53 54
f 55 56 57
f 58 59 60
f 61 62 63
f 64 65 66
f 67 68 69
f 70 71 72
f 73 74 75
f 76 77 78
f 79 80 81
f 82 83 84
f 85 86 87
f 88 89 90
f 91 92 93
f 94 95 96
f 97 98 99
f 100 101 102
f 103 104 105
f 106 107 108
f 109 110 111
f 112 113 114
f 115 116 117
f 118 119 120
f 121 122 123
f 124 125 126
f 127 128 129
f 130 131 132
f 133 134 135
f 136 137 138
f 139 140 141
f 142 143 144
f 145 146 147
f 148 149 150
f 151 152 153
f 154 155 156
f 157 158 159
f 160 161 162
f 163 164 165
f 166 167 168
f 169 170 171
f 172 173 174
f 175 176 177
f 178 179 180
f 181 182 183
f 184 185 186
f 187 188 189
