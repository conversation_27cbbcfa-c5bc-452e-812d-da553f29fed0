24 Aug 2022 - 0.12.0    CRCHash experimental instruction, fix for a rare append bug, imptype instruction new behavior (check manual), fix for get float/double, fix xmath with multidimensional arrays, Print hex line feed, fix for B/E/C String operators, fixes and improvements for RSA encryption and keys, calldll supporting points to variables and return value, various patches for improving compilation, better support for -x option, some improvements of reimport mode mainly related to variables and hashing encryption, support for tar output with -O option, fix for rot/xor encryption, additional compressions, fix for a memory bug triggered by ttgames.bms, many other bug fixes and improvements
05 Apr 2021 - 0.11.0    SearchArray command, Reimport command, additional options in quickbmsver, idstring and findloc now supporting wildcard single bytes (up to 32), better support for some operators in xmath, String now works with binary data (may break compatibility with some scripts), GARBro and other compressions, choose algorithm in lz77wii_compress, improved PKCS5_PBKDF2_HMAC and BytesToKey, added Rfc2898DeriveBytes, removed tcc symbol that didn't work on Win98 and added few others, calldll supporting entrypoint/rva2file/vatofile/fileto2va/file2va, conflicting symbols from some compressions now static, solved append -1, get filename/path/* on sockets and http links now returns better results, slog to memory_file, fixed bug that asked to overwrite files in append mode, fixed some strings parsing in Encryption EXECUTE, fixed Encryption REPLACE, support for unicode filenames in command-line and in directory scanning, fixed bug in creation of folders with unicode names, visualization of additional info in case of error, added support for MEMORY_FILE[FILENUM], librtomcrypt recompiled without LTC_ARGCHK error, experimental -y option for json output, some optimizations of multidimensional arrays and String X, opening of TEMPORARY_FILE not affecting the names of the input (filename/fullname/basename), -e option in reimport2 mode for disabling compressions, full list of commands in the -c option, endian argument for reverseshort/long/longlong, SLog ID argument for selecting reimport of strings, fix clean_filename rare bug, C structs now require ok from user, fix for get longlong, json deserialization (String X) now handles backslash escapes, String = supporting the current endian, filexor/filerot/filecrypt fix negative offset, SLog with binary type produces an output with all bytes in hex (cstring), String H operator for converting variable to cstring, condition fix for some rare binary strings, condition new prefix 0 working like String, Print colors, FindLoc regex, the FILENUM argument of filexor/rot/crypt is now working (default behavior is all files as before), getarray/putarray multiple arguments, fixed bug in calldll with over 12 arguments, Get VAR ??? for prompt from user, continue/break/label fix, putvarchr longlong, clang support (tested on Termux Android), gcc 10 support, get variable3/variable4 64bit, swprintf removal, oodle (kraken/lzna/bitknit) support on Linux, lz4x_compress fix, Append mode 2 for insert mode, Append mode also working with Put* commands, tcc long long support, putdstring with negative size, -J using VARSZF instead of VARSZ (may cause issues), Encryption Math fix for using variables, improved xmemdecompress on Linux, new code for ppmdh and ppmdi, dump of command-line arguments if invalid, various improvements and optimizations
20 Oct 2019 - 0.10.1    added some few commands similar to 010 Editor, additional work-around for handling multiline strings, full support for float operations and visualization (get/put/math/print/calldll), automatic fixing of ASIZE variables in reimport3, experimental unicode32/utf32 type, fix for rare FDSE issue in GUI mode, d3des and chacha20 encryption, improvements in tomcrypt support, fix for Encryption algorithm string, rotor default value is now 6, ZIP_AES now fully working, CallDLL Python, CallDLL Lua, fixed crash if no RET in Calldll, added some symbols (like printf) in calldll tcc, rare issue with multidimensional arrays
28 Apr 2019 - 0.10.0    PREFIX_OPENSSL for Mac Makefile, single quoted characters bigger than 0xff, fix for some C_struct alloc/free, quickbmsver -32 -64 -F, fix for rare reimport mode filexor, reimport mode full support for multiple reversed math operators, reimport xsize support, signed numbers in Set, fix for calldll with some function names and better support, calldll more arguments, -j option for Slog output in UTF16 unicode, -b C option for filling the *Log empty data with custom byte, default filler is now space in Slog and still zero in *Log, reimport3 mode with automatic shrinking and enlarging of the file if there is no offset, SLog in reimport mode can read UTF8/UTF16LE/UTF16BE, fixed and removed warning in SLog, zlib/deflate reimport fix, -f also works with nameless files, fix for some rare xsize parameters, restored support for empty input file argument, some fixed and new compressions, 0x00 bytes in SLog size-based unicode
25 Nov 2018 - 0.9.2     fixed make_dir and comtype copy bug bugs introduced in 0.9.1, improvements for long_name_support function and its alternative use for chdir and stat, reimport mode for nameless files on linux, fix for lz4x that took 220 Mb of memory for nothing
22 Nov 2018 - 0.9.1     mydown library fix, various fixes related to BytesRead_idx, build_filter, -X and sockets, filexor/rot/crypt reset position when a new filenum 0 is open, QUICKBMS_CRC_TABLE var in verbose mode, QUICKBMS_*_FOLDER variables available at runtime, findloc fix, math mul/imul, direct_var_alloc fix, many fixes in CMD_Set_func, memcpy replaced by mymemmove/STR_MEMCPY for fixing some current and future issues, comtype.h (no longer needed to check defs.h with comtype_scan2), some new compression algorithms and updated libraries, encryption key visualization in verbose mode, encryption rc4_nokey, Capstone disassembler engine supporting many architectures, added another algorithm in encryption random, libtomcrypt update, fixes for quickbms.dll, added many fake compressors used in reimport2, -# option in reimport mode for reimporting only the modified files skipping the others, -U option for listing all the compression algorithms available in quickbms, some new guessed extensions, zstd aluigi fix, important fix for non-english unicode folders and a rare bug caused by multiple path delimiters in non-english filenames, removed ccs=UTF-8 in file opening for using quickbms.exe with Wine again, cstring decoding error if input is wrong, fix for the -f #NUM feature, reimport_4gb_files.bat, tentative of supporting nameless files in append mode, output_folder set to "" will use the folder of input file, revision of runtime help, quickbms_arg var containing whole -a input, some command-line options switched from toggle to fixed value for avoiding wrong double usage, fix for fgetss empty line in quickbms_4gb_files, fix for GUI input file selection in Win98
17 Jun 2018 - 0.9.0     fix bms parsing from buffer, backslash escape with quotes at end of bms line, c_structs fix, extracted/reimported files statistics separated from log operations (append mode), filecrypt disabled during log, String VAR S empties all arguments before setting them, String n (byte2num) N (num2bytes) and U (base64) operators, RNG available in encryption, call/startfunction can set caller arguments with new values, added variable6 and variable7 types, improvement of encryption increment, various updated/improved/new/fixed compressions, increased buffer for selected files, IPC interface with -W option (web/namedpipe/mailslot), type ? when selecting bms script to load the clipboard content, -f #NUM for extracting only the file number NUM, improved xml_json_parser, fast SortArray, special files myfseek, namecrc fix, quickzip fix, Open filenum 0 redirect mode, improvements for Open command now with FDDE2 and FDSE2, -0 works in reimport mode too, ucl/lzo/bzip2 are no longer external, clog with zero-bytes uncompressed files, vspr va_list fix
07 Mar 2018 - 0.8.4     fix and warnings for reimport2 with sequential offset, nvache compression, fix for putvarchr using strings, cipherfinal support for openssl algorithms, BCryptDecrypt encryption, backtrace.c 64bit compatibility, fix for fdnum indexing in file.c, fix for slog in reimport mode with encryption filecrypt, append mode visualization symbol, comtype explode in reimport mode, now reimport2 clears the original space if new file is bigger, fix for qfs compression, base64 small fix, swprintf gcc 7, fix for String VAR t VAR, lz77wii in reimport mode and level5_compress, comtype html/xml/json
21 Jan 2018 - 0.8.3     math rol/ror for 8/16 bits, few file number assignment fixes in cmd.c, prs_8ing_compress, now file numbers can be variables too, fixed bug introduced in 0.8.2 in OpenSSL rc4 (probably still affecting openssl 1.1), better visualization of -B with small files, automatic filename if Slog uses empty name, fix for mydownlib with openssl 1.1, the invalid Idstring VAR FILENUM is no longer supported
08 Jan 2018 - 0.8.2a    micro bugfix for putvarchr in 0.8.2
07 Jan 2018 - 0.8.2     new alternative reimport mode activated with -r -r, other options available in quickbmsver, String v operator acting like CSV, some new compression algorithms, small fix for putvarchr used with variables, Encryption flip for reversing bits, fix for OpenSSL 1.1, new -B option for dumping the unparsed data of the input files, small fix for some coverage statistics, fixed bug in reimport mode with renamed duplicate files, the -. option now allows to reimport data in some header/data builder scripts, comtype copy no longer allocates the full size of the file in memory, experimental support for http:// and https:// input files, important fix for selecting specific files in GUI mode, -J option for forcing all the constant strings as Java/C escaped strings, better support for RIFF files when guessing extensions, xmath supporting also 'char'/time/octal/binary
09 Sep 2017 - 0.8.1     better Makefile compatible with MacOSX (thanks Misty De Meo), Endian Set CURRENT_ENDIAN, PutVarChr VAR 0 &VAR Long for storing VAR or MEMORY_FILE address to use with external DLLs, fixed a bug in FindLoc, improved incremental_fread, new compression algorithms and updates, fixed bug with CallDLL and arbitrary offsets, CallDLL imagebase and address features, fixed small bug in RNC compression introduced in 0.8.0, some fixes in ntcompress and lz77wii, now all the lzrw* algorithms don't check the useless 32bit flag field, xmemcompress working on Linux too, -N option for using decimal sequential names like 0.dat instead of 00000000.dat, -e option to ignore compression errors (debug), fixed rare bug with output filenames in append mode, fixed bug with custom lzss_compress EJ parameter, fixed yappy_compress, fixed bug with -9, added tga and uasset in sign_ext.c, improved compatibility with gcc 7, added json formatter in String, fixed an important bug in unicode strings introduced in 0.8.0, fixed NULL pointer in some VAR[i], String X parameter for experimental parsing of XML and JSON strings, OpenSSL 1.1 compatibility
12 Mar 2017 - 0.8.0     Break and Continue instructions fixed and available, experimental Label instruction (use Break/Continue to go to that label), embedded C compiler to be used with CallDLL and the tcc calling convention, C compiler available also with the tcc variable type, updated compression algorithms and added few new ones, fixed a bug with gussed names with -d/D and TEMPORARY_FILE, fix for encryption mode of rotate, better handling of the rnc compression, fixed bug in xchdir, bug in utf8_to_utf16_chr, fixed a problem with if statements having more than 2 conditions, the user will be asked only once to confirm the loading of external dll, -P option to set the default codepage, restored compatibility with Win98, correct filenames output for non-ansi names, -T option for not deleting the TEMPORARY_FILE
30 Nov 2016 - 0.7.7     macosx compatibility, fix for reverse FindLoc, fix for Print with -Q, removed tag SCRIPT's MESSAGE when using Print, added _ and . trim operators in String, new and updated compression algorithms, SortArray switched from signed to unsigned (more useful), fix for comtype sixpack, lbalzss and SCUMMVM9, coverage statistics showing also the current offset, better handling of If statements with values coming from arrays, -K option, fix for tea_setup in encrypt mode
22 Aug 2016 - 0.7.6a    fixed unicode endianess in internal code
22 Aug 2016 - 0.7.6     fixed bug in FindLoc introduced in 0.7.5, compatibility for opening paths and filenames containing non-english characters (like chinese/korean/japanese directories of MMORPG), Oodle 2.3.0 (now fully working), some new compression algorithms, hsel encryption, automatic handling of endianess in EDL1 and EDL2 compression, removed backtrace library used during crashes to decrease the size and granting compatibility with Windows 98, fixed bug in check_wildcard for options -f/-F, fix for recognizing MEMORY_FILE set as key of encryption and other commands
18 Jul 2016 - 0.7.5     additional argument for FindLoc to specify the ending search offset, fixed a bug in FileXor/Rot/Crypt introduced in 0.7.4, small fix for the optional arguments of NameCRC, small fix for a rare bug in String, improved printf and sscanf operators of String, new compression algorithms, updated Oodle, isaac encryption, FreeLibrary called in CallDLL at the end of the script, -Z option for replacing all the archived files with zeroes in reimport mode, warning about impossibility to reimport files with wildcards, fix for the XSIZE field in the Log/CLog commands, fix comtype wp16, fix for recursive_dir with invalid sub_folders, improvements for set_utf8_to_unicode and set_unicode_to_utf8, realloc with usage of temporary file in case of failure with big buffers, updated algorithms and libraries, improved guessed extensions for some nameless files
29 Apr 2016 - 0.7.4a    fixed a bug in big endian Get byte/short introduced in 0.7.4
21 Apr 2016 - 0.7.4     fix for String 0 operator, Do While working with multiple conditions, Endian works also with variables containing the saved value, CallDLL usercall fix, added various compression algorithms, fix for filexor/filerot with negative offset, fix for overflowing longlong values read/written using quickbms.exe, simple progress visualization for reimporting, fixed reimporting of MEMORY_FILEs
31 Jan 2016 - 0.7.3     fixed the recent issues of quickbms 0.7.2* (back to 0.7.1 method), some new compression algorithms, fixed identification of comtype ppmdi_raw, fix and improvements for the parameters of the *tea encryptions, improvement of ZIP_AES, various parts of code set as static, small improvements of rsa_tomcrypt, String t and T operators to make easier the handling of html/xml
12 Dec 2015 - 0.7.2a    fixed a bug introduced in 0.7.2 and related to the length of the variables
11 Dec 2015 - 0.7.2     now the TEMPORARY_FILE is deleted automatically at the end of the extraction without asking, added the following compression algorithms: PKWARE DCL, IBM TERSE (PACK/SPACK), PKWARE reduce, a configurable LZW engine, ultima6, lz5 and yalz77, 4 additional formats for RSA keys, fix for sortarray with arrays having different elements, SLog supporting offsets till 4Gb - 1, reimport mode for deflatex/zlibx, added ZIP file creation in addition to the ISO one, -Q for really quiet mode, updated some algorithms, increased the number of allowed arguments per line for If and String S, setvbuf to 64Kb (probably useless), fix for Xmemdecompress with lzxnative and lzxtdecode formats, replaced sub_var strdup_replace with manual alloc+memcpy, feature to embed scripts inside quickbms.exe for distribution in modkits
16 Nov 2015 - 0.7.1     many fixes: stricmp crash on Linux/WinXP, String =, set basename, putarray, recognizing end of file in fgetxx, coverage statistics of memory files, skip existent files choice/option, double request of files overwrite in some cases, some rare compression algorithms not working correctly, mcrypt comma separator, a couple of magics in sign_ext.c. -i option for creating an ISO image, now some compression algorithms don't give error if the end of output is reached (better for the comtype scanner), undk2 replaced by refpack_decompress_safe, more operators in encryption random and support for bitmasks (like key >> 16), my encryptions like random/math/xmath now support also variables in their configuration, added aes modes ige/bi_ige/heat, less resource used by append log to file, initialization of arrays with multiple input files, 30 new compression algorithms, code improvements and less memory consumed at runtime
31 Oct 2015 - 0.7.0     support for wildcards in the selection of input files, fixes in check_condition: constant strings, basename, filepath and filename, String reverselong/short/longlong weren't recognized, new 'y' String operator for alignment, Set to_unicode for converting to utf16, new FULLBASENAME type, better Print hexdump, String with constant strings, important fixes for the String operators: shl, mul, equal, split and cstring, some new compression algorithms, TEMPORARY_FILE now doesn't prompt for overwriting, better handling of same input and output file, Encryption Math with #INPUT#, some default values to the few compressions that required a ditionary value, LZ4 supporting dictionary and returning no errors in case of short output, fix for filexor/filerot with negative values, fix for utf16 conversion, information and fixes in the manual, reimplemented lzrw, balz and quad compressions, added mcomp/libmcomp, irolz, uclpack and ace compressions
14 Sep 2015 - 0.6.7a    full lzham implementation with parameters brute forcing, better support for MEMORY_FILEs in C structs
13 Sep 2015 - 0.6.7     variables between quotes are now considered constants, fixed some math/xmath operators, fixed Findloc unicode, some new compression algorithms, experimental Scandir modes for working with processes, quiet option, fix for Open EXISTS in reimport mode, fix for avoiding to create new files with -w using Open, small improvement of comtype lzma_dynamic, fix for some compresison algorithms that take parameters from the dictionary argument on quickbms_4gb_files
04 Aug 2015 - 0.6.6a    support for negative numbers in String <<, yay0dec compression, fix for the cpk recompression algorithm, but note that it can't be used in reimport mode because the decompression is size-dependent and that value (FileSize in cpk.bms) is left unchanged in reimport mode
02 Aug 2015 - 0.6.6     experimental reimporting of chunked files, fix for Set VAR strlen VAR, NameCRC allowed to use a MEMORY_FILE (useful for compressed embedded list), fix for NameCRC that skipped hex hashes, added Zopfli for better compression of some files, a couple of new compression algorithms, set correct position of memory file in append mode, don't ask to overwrite in append mode if the file has been created by the script, -O - for stdout output (redirect may not work), fix for long directory names
08 Jul 2015 - 0.6.5     SLog command for exporting/reimporting strings (game localization), a couple of new compression algorithms, fixed various recompression algorithms, improvement of RSA and added similar encryptions, added most of the eCRYPT algorithms, compatibility with Windows 8.1, fixed a very rare bug in putarray, some minor fixes
29 May 2015 - 0.6.4     encryption PKCS5_PBKDF2_HMAC, BytesToKey and ZIP_AES (now zip.bms supports AES too), updated many compression algorithms (LZ4 was necessary for Witcher3), small fix for unzip_dynamic/bzip2
06 May 2015 - 0.6.3a    fixed a bug in Strlen introduced in the previous version
01 May 2015 - 0.6.3     new Codepage command for utf16 to utf8 strings conversion (will be improved/expanded in future), base conversion in Math command (binary/octal), fix for the parameters in the Print command after the pipe, many new compression algorithms including also recompression (cpk, bpe and nrv are the most important), new argument for Strlen to get the full size of the variable, initial implementation of rsa encryption, command Append 1 improved to work with Goto, additional parameter in xor_prev/next to specify the operation on the last byte, fixed a bug in CallDLL introduced in the last version, QuickBMS version and information visible in the properties of the exe, restored compatibility with Windows 98 (_fstat64 caused by <iostream>)
01 Apr 2015 - 0.6.2     tons of new compression algorithms, new Makefile for Linux, calldll from encryption/comtype with #INPUT_SIZE# and #OUTPUT_SIZE#, String with multiplication and hex2uri/uri2hex operators, a '0' added to the String operator allows to set an empty VAR1 in case of errors, rc6 encryption, xor_prev/xor_prev2/xor_next/xor_next2 encryptions, fix for getarray, added adler32 to the available crc functions, fix for a rare bug in reimporting, append 1 now allows to place the new content at the current position of the output file, source code of QuickBMS in a separate zip archive to avoid space and confusion, fix for using libtomcrypt, activated all the rnc compressions available, fix for xmemdecompress of native files (0xed magic), cleaning of spaces at the end of folder names in extraction, fix for filepath type
28 Dec 2014 - 0.6.1d    support for long filenames (rarely used)
18 Dec 2014 - 0.6.1c    fix for Get basename, fir of the description of String >>, support for String % with a string as second variable (same behaviour of >> but from the beginning instead of the end), removed the double backslashes in some internal full paths, fixed Linux compatibility
10 Dec 2014 - 0.6.1b    fixes for the puyo compressions, fix for the visualization of the latest line of the script in case of errors
08 Dec 2014 - 0.6.1a    removed the visualization of the latest script line when there are no errors
07 Dec 2014 - 0.6.1     most of the global variables now have a g_ prefix, new NameCRC instruction for working with list of filenames, support for full scripts passed as command-line argument (';' separated), support for C NULL delimited arrays like short var[], many new crc/hash algorithms for the encryption and namecrc commands, added basename/filepath/filename comparisons for conditions, fix for math x with big numbers, added Set filepath, String P mode to use the same syntax of the Print command, enhancement of the Comtype dictionary to allow data containing zeroes, added new compression and recompression algorithms, mpq encryption, fix for the prs compression, more information when a read and compression error occurs, option -M for extracting only the files different than those available in another folder, no folders creation with -0 and -O, QUICKBMS_FILENAME internal variable, updated some libraries, zlib_noerror and deflate_noerror automatically increase the uncompressed size if needed (they use the same code of unzip_dynamic), Xmemdecompress with automatic decompression of Xbox files compressed with xbcompress (lzxdecode and lzxnative), lzma_dynamic with automatic scanning of the flags in case of failure, small fix for some gzip files, other small fixes and improvements, support for C enum, idstring information in -V mode, visualization of the last script line that caused the error, updated some libraries like lzma
25 Jul 2014 - 0.6       review and better writing of quickbms.txt, source code extension H to C, -a can be specified multiple times for new arguments, new argument for the Append command to choose when and how write the new data, added new names for String operators, Endian can save the current endianess in a variable, Get line works even if there is no new line delimiter at end of file, last offset visualized when a compression fails, additional arguments for String printf, fix for base64_compress that wasn't handled, added comtype lz4_compress, new behavior of Debug command, base_offset argument for Padding command, negative index variable for get/putarray for taking and storing elements at the end of array, a positive value in goto SEEK_END is automatically converted in negative to seek correctly, fix for the filenames ending with . or * to guess their extension, better colors for the hexhtml visualization, automatic handling of xcompressed files (\x0F\xF5\x12\xEE) in comtype xmemcompress, some malloc to calloc changes, new icon, changed major version number due to the huge amount of enhancements from 0.5
19 Jul 2014 - 0.5.32    fixed memory consumption with files without names, fixed unicode conversion with no delimiters, added a math/xmath operator for strings, added new comptypes for handling lzma/lzma2 without header/prop (like those used in 7z files, previously known as msf), added hmac hashing using "hmac algorithm", updated various compression algorithms, added variable5 type, added QUICKBMS_HEXHASHL for low case hash, can specify lzma prop using dictionary, fix for multi dimensional arrays, alignment in read/write process memory
06 Apr 2014 - 0.5.31a   fix for already aligned size when using *log xsize
05 Apr 2014 - 0.5.31    improvements for quickbmsver, *log with the xsize value used for reading aligned data (sometimes useful with block ciphers), fix for the xmath command that now works with unsigned numbers, String 'f' operator to filter non alphanumeric chars, improvements for the scexpand compression, added a new crc algorithm, option to use case sensitive variable names, additional work-arounds for gzip in case of invalid fields, encryption hash that performs hashing on the string specified as key if available (very useful), added tons of hashing algorithms thanks to sphlib
17 Mar 2014 - 0.5.30    fixed multiple If, endian guess VAR, handling of some unicode spaces in the scripts, implemented correct disabling of the secure allocation, Math with reverse operations, tons of new decompression algorithms and some new recompressions, hex visualization of the encryption key in verbose mode, some new experimental lzss window initilizations, update of some libraries, more details for the exceptions, more details for the lzma errors
28 Jan 2014 - 0.5.29    automatic guessed extension for the filenames that have a dot or a * at the end, replaced the Scummvm RNC compression with the old one because it didn't work, fixed lzlib compression, updated various libraries, backtrace after crashes, Windows 8.1 fix, fix for CallDLL with a MEMORY_FILE of same name but different content, set VAR ? ? to allow the user to choose the content of the variable at runtime, If case sensitive if used the 'u' optional parameter, added ZPAQ compression, added more return values in case of lzma errors, usage of variables in encryption random, autostart with -9 if quickbms crashes just when launched, fix for open FDSE in gui mode, reimport.bat
28 Oct 2013 - 0.5.28    added the possibility of specify quoted strings on multiple lines with automatic \r\n added with each line, If statement with strncmp and added additional names for the other checks (like strstr, strcmp and so on), new alternative math/xmath operators, encryption xmath, encryption random (experimental), changed syntax for encryption math, new String operator S for splitting a string in multiple variables, a new compression, updated disasm engine, improved memory read/write function (process.h), fix for debug string visualization, filexor/filerot supporting also textual key, additional choice 0 (zero) for skipping all the existent files at runtime
18 Oct 2013 - 0.5.27    multiple conditions in the If statements, usercall calling convention, Next allows to specify a math operation, new compression algorithms, int3 option working also with compression and encryption, fix for String printf and int3
06 Oct 2013 - 0.5.26    added some new compression algorithms, -k option to automatically skip existent files, usage of PAGE_GUARD instead of PAGE_NOACCESS to avoid issues with bugged drivers (Xonar and so on), experimental XMath command for multiple simple maths in one line
22 Sep 2013 - 0.5.25    added the Prev command to decrease a variable in a For cycle, comtype dictionary as variable if size specified, String C string with x operator, lot of new compression algorithms mainly from Scummvm, verbose -3 option, exception handler with additional info, \u unicode in C strings
11 Aug 2013 - 0.5.24b   work-arounds for the variables optimizations introduced in the previous version
04 Aug 2013 - 0.5.24a   fix for FDDE/FDSE with ? parameter, usage of static variable name and value buffers for faster performance
20 Jul 2013 - 0.5.24    fixed a crash with -9 at the end of the process, CallDll with automatic handling of stdcall functions exported with or without @ name, sega_lzs2 with automatic handling of headers and size, automatic handling of nameless files inside folders (like folder\), -X option that acts as -H for the Windows console (colored fields highlighting with hex viewer), better support for Snappy, another small fix for -d/D, less memory requested for -X/H, additional checks to verify if the reimported file is bigger than the original
29 Jun 2013 - 0.5.23a   improvements for the -d/D options
29 Jun 2013 - 0.5.23    -O option to redirect any extracted file to the same output file (good to be used with named pipes), exit/error codes, real unicode utf16 support in the bms commands, -Y to answer yes automatically to any request from the tool, full fix for -d/D and support for same input and output folder ("_extract" suffix), fixed getarray with constant variables, fix for -9, -3 option to place INT3 before any CallDll, support for Windows 98, ffce algorithm, exception handler, added changelog.txt
09 Jun 2013 - 0.5.22    speed improvements and some fixes, removed paqv6 and lpaq8
02 Jun 2013 - 0.5.21a   fix for lz4 output size, -D option similar to -d but without folder with name of the file, -f/F filter with negation filter like -f &quot;*.mp*;!*.mp3&quot; (which takes all files with mp* extension except mp3)
28 May 2013 - 0.5.21    fix for -d option, fix for slz and automatic parsing of SLZ header and output size, option for not terminating in case of file errors
07 May 2013 - 0.5.20b   fixed reflection in CRC engine
07 May 2013 - 0.5.20a   some small fixes and improvements for the CRC engine
05 May 2013 - 0.5.20    fix for the selection of multiple files, better crc engine, -f/F with support for stdin, fix for LZMA recompression
13 Apr 2013 - 0.5.19    improved filters options -f/F that now support multiple filters and even a text file as input containing all the desired filters
06 Apr 2013 - 0.5.18    replaced kzip with uberflate, some bugfixes (comtype dictionary and calldll), lzma recompression now available also on Linux, added the -u option to check updates
21 Mar 2013 - 0.5.17c   fixed a problem in reimport mode and added some new lzma modes
08 Mar 2013 - 0.5.17b   fixed the multidimensional arrays and improved the debugging feature with auto-resume of breakpoints
07 Mar 2013 - 0.5.17a   fixed a bug introduced in 0.5.17
06 Mar 2013 - 0.5.17    endian command with switchable endianess, calldll with RET pointer, fix extraction of files called as memory/temporary files, fixed quickbms.txt, support for floating numbers in String p=, fixed encryption in reimport mode, improved get/putarray, added variable4 used in Battlefield, experimental multidimensional variables VAR[i][j], experimental debugger in the process input (implemented on-the-fly for Simraceway), added the setting of the console title with the most useful information, fixed slash/backslash in the -f/F filters, added {} other than * in the filters, other fixes
29 Jan 2013 - 0.5.16c   added support for the DFLT compression used in the ttgames.bms script that now is stable and supports all the LEGO series
28 Jan 2013 - 0.5.16b   fixed a bug introduced with the previous update
28 Jan 2013 - 0.5.16a   fixed a problem with Open FDSE in scripts used from command-line with a relative filename
27 Jan 2013 - 0.5.16    added the cabextract compression algorithms, signed variables useful for Get, info about the amount of file covered by the script, processes and modules, possibility of renaming an invalid output filename just by pressing RETURN, use * to select whole folders and subfolders when using QuickBMS with double-click, better performances during the scanning of folders, -S option for executing a program on each extracted file, checked compatibility with Linux (Debian/Ubuntu)
05 Nov 2012 - 0.5.15b   minor fixes, --version and a new compression algorithm
01 Sep 2012 - 0.5.15a   added the -s option and fixed the handling of the . output folder
01 Sep 2012 - 0.5.15    some small fixes
24 Aug 2012 - 0.5.14    FindLoc supporting variables, SortArray command, quickbmsver with possibility of specifying the -9 option, experimental encryption/comtype with embedded calldll, improved putarray/getarray, some minor fixes and enhancements
06 Jun 2012 - 0.5.13    added some less known compression algorithms and a little fix to the EXECUTE command
01 May 2012 - 0.5.12    added some compression algorithms and fixed get line
25 Apr 2012 - 0.5.11    fixed -DDISABLE_SSL, added slz_03 and ntcompress30/40, filexor 0x11223344
05 Apr 2012 - 0.5.10    fixed a bug in the reimporting in case of filenames starting with slash/backslash, added the RCN recompression, allowed the usage of the secure free() with non secure allocated memory (otherwise it's not possible to free memory allocated by external libraries)
21 Mar 2012 - 0.5.9b    fixed a rare bug with the binary type
17 Mar 2012 - 0.5.9a    fixed lzf recompression
02 Mar 2012 - 0.5.9     added some new compression algorithms, optimization and customization of the secure allocation functions (they can be disabled using the option -9, needed in some occasions due to some limitations of this feature), fixed a bug that specified an input folder in case of selection of multiple files
20 Feb 2012 - 0.5.8     added the Include command and some checks
12 Feb 2012 - 0.5.7     speed and resource optimizations for the memory
02 Feb 2012 - 0.5.6d    customizations of the tea, xtea and xxtea algorithms and some fixes
27 Jan 2012 - 0.5.6c    fixed a compability problem with the gcc optimizations regarding the patch for kzip
27 Jan 2012 - 0.5.6b
27 Jan 2012 - 0.5.6     added various other compression algorithms and optimized the usage of kzip
23 Jan 2012 - 0.5.5     added the EXECUTE method for both Comtype and Encryption, String scanf/strstr/strrstr, various new compression algorithms, usage of kzip.exe in reimport mode if the compressed size is bigger than the original one, fixes and optimizations
09 Jan 2012 - 0.5.4a    added a slower but more secure way to manage the allocated memory, it's a crazy experiment I had in mind and may help during the debugging of some scripts
19 Dec 2011 - 0.5.4     solved some bugs but there is still lot to do, created also an encryption_scan.bat+bms solution for scanning the various encryption algorithms with a known key and optional ivec
04 Dec 2011 - 0.5.3b    fixed a problem with Open FDDE/FDSE
14 Nov 2011 - 0.5.3a    added the possibility of automatically setting new filenames for those that already exist (choose 'r' when prompted)
25 Sep 2011 - 0.5.3     now it's no longer needed to specify the output folder when used from the command-line, added arguments to the CallFunction command, enhanced the unzip_dynamic compression, fixed a bug in the recompression of XMemCompress
01 Jul 2011 - 0.5.2     finally an homepage for this project, many fixes and additions some of which are time, time64, clsid, ipv4, ipv6, assembly types (yes now it can be used as a quick assembler/disassembler), rotate, reverse and pc1 encryption, msf compression, new experimental input and outputs with SSL added to the socket interface, the -H option for a html output of the parsed file format and much more
08 Jun 2011 - 0.5.1     updated the supported calling conventions, added experimental support for network sockets and processes, various fixes for the write operations, added rotate encryption and a new useless option for the math one
31 May 2011 - 0.5       updated some libraries, added libkirk and nitrosdk, incremental xor and rot encryptions, aes ctr, added the new types: float, double, variable, variable2 and variant, new cool debug mode, various fixes, allowed the reimporting of nameless files (like 00000000.dat), experimental parsing of C structures, handling of multiline comments, added falcom recompression and dragonballz decompression
11 Mar 2011 - 0.4.10b   fixed a bug in quickbms64_test
10 Mar 2011 - 0.4.10a   fixed a small problem of the reimport mode happening in some rare cases when encryption is used
09 Mar 2011 - 0.4.10    fixed a bug in the reimport option and added various recompression algorithms
08 Mar 2011 - 0.4.9a    only fixed the xxtea encryption
06 Mar 2011 - 0.4.9     added the experimental -r option that allows to reimport the extracted files or some of them without modifying the scripts (yeah finally it's possible), added the lzo1/1x/2a, gzip and lzss recompressions, fixed the behaviour of unzip_dynamic, fixed and enhanced the automatic extensions, FileCrypt command, Math and Swap encryptions
26 Feb 2011 - 0.4.8c    fixed a bug in the automatic setting of the extension for nameless files and one in unzip_dynamic
15 Feb 2011 - 0.4.8b    fixed a stupid error in the extraction function that allocated one gigabyte instead of one megabyte
21 Jan 2011 - 0.4.8a    micro fix to avoid NULL pointers
20 Jan 2011 - 0.4.8     added putbits, fixed a possible problem in write mode, added the R string operation, fixed the reading of the filexor/filerot keys, now the extracting of files non compressed and non encrypted should take almost no memory, experimental quickbms64_test version for archives and files bigger than 4 gigabytes, various fixes and enhancements
04 Oct 2010 - 0.4.7     solved a bad bug in CallFunction, some enhancements
01 Aug 2010 - 0.4.6a    removed the FindLoc modification I added in the last version, added a verbose message when Idstring doesn't match, added the B/E/C operators in String that work on null delimited strings
28 Jul 2010 - 0.4.6     added the COMPRESSED method used to store big amounts of data in the scripts using less bytes (zlib plus base64), experimental support for libtomcrypt, the -E option that allows to change the endianess of a file on the fly by simply knowing its format and reading it, -d option for creating an output folder with the name of the input file, support for variables in FindLoc, support for SEEK_CUR and SEEK_END in GoTo, rnc and pak_explode compressions, r operator in String for reversing strings and = for converting numbers to strings
01 Jun 2010 - 0.4.5     added some enhancements to the Print command, encryption mode for rot, an experimental printf-like operator for the String command, variable used for the Padding command, small fix in the Open command
28 May 2010 - 0.4.4a    fixed two bugs started a couple of versions ago in FindLoc and Get/PutArray
11 May 2010 - 0.4.4     some micro enhancements, added the possibility of recompressing data with the zlib, deflate, lzo1, bzip2 and XMem algorithms (note that QuickBMS is and will remain an extraction/unpacking tool so this is only a just-for-fun feature)
30 Apr 2010 - 0.4.3     solved the big usage of memory caused by the quad/balz compressions, reduced the amount of used memory in some occasions, bugfix and enhancement for zipcrypto
27 Apr 2010 - 0.4.2b    bugfix for unzip_dynamic and bzip2_file
26 Apr 2010 - 0.4.2a    only a little customization of the Open command so that the creator of the script can decide to terminate or not if the file doesn't exist
25 Apr 2010 - 0.4.2     now the user that use the tool through its gui (double click on quickbms.exe) can select multiple archives to handle, added additional compatibility with the WCX plugins
23 Apr 2010 - 0.4.1     added support for the WCX plugins used in Total Commander, added tons of new encryptions, support for CRCs of any type, updating of some external libraries (like zlib, lzma and ppmd), some small enhancements and bugfixes
12 Apr 2010 - 0.4       automatic folder and extensions when it's used no filename in the *log commands, reintroduced all the openssl algorithms manually, fixed and enhanced some features of CallDll, changed the behaviour of lzhuf (it took the decompressed size from the data), improved handling of less known gz/z files, added the pack compression, note: remember to check ever the list of available scripts because I update and write new ones often, for example majesty2.bms is now complete and compatible with any version
10 Apr 2010 - 0.3.15a   only an useless bugfix to make "" a constant and added another math operator
09 Apr 2010 - 0.3.15    added unsigned If/Elif/Else, some new compressions or enhancements, radix and power math operations, some new and useless Set types, byte2hex/hex2byte/compressions/encryptions/toupper/tolower in the String command, support for any encryption and even any hashing algorithm supported by OpenSSL, usage of dlls as MEMORY_FILE in CallDLL, support for any calling convention: msfastcall/borland/watcom/pascal/safecall/syscall/optlink/clarion, reset of memory file positions in case of multiple input files, fixed a micro bug when using comtype2_scan, exe compiled with the latest OpenSSL (that's why its bigger)
10 Feb 2010 - 0.3.14c   added only 2 compression algorithms used in Pacific Assault and a work-around for some rare gzip files
04 Feb 2010 - 0.3.14b   added the copy compression that could be useful in rare cases when handling data encrypted with block ciphers like AES and blowfish
04 Feb 2010 - 0.3.14a   now the ivec in Encryption can be also a variable (useful for decrypting the xbo files of VBS2)
01 Feb 2010 - 0.3.14    added tons of new compression algorithms and the 'u' option in the Math command for forcing the unsigned operations
25 Jan 2010 - 0.3.13    added only an additional work-around useful with some XMemDecompress streams, the possibility of specifying a wildcard for ScanDir not only through -F but also in the same script, bzip2_file for decompressing bzip files without knowing their output size, -a option for specifying variables visible inside the bms scripts, this allowed to use a new comtype_scan2.bms script for testing all the supported compressions without editing it
01 Jan 2010 - 0.3.12b   added only the CRI CPK compression
30 Dec 2009 - 0.3.12a   some enhancements for the rebuild mode (-w option), the rest is untouched
21 Dec 2009 - 0.3.12    added the GetBits command, the possibility of calling functions without restoring the variables at the moment of the call, removed the problem of using the Log commands with filenames that have a number as first char
18 Dec 2009 - 0.3.11    added the CryptDecrypt and CryptUnprotect encryptions, some new compression algorithms (prs, puyo and falcom) and some enhancements
02 Dec 2009 - 0.3.10    added the xxtea encryption, calldll with memory_file, asking of deleting the TEMPORARY_FILE if used, possibility to use stdin as input file (use -), the For instruction now can be initialized with any math operation (example: For i -= 10 To FILES), Put/PutDString/PutCT commands which allow to write inside a file/memory_file, compression algorithm used in STALKER and some minimal things (for example: Set VAR1 = VAR2)
27 Nov 2009 - 0.3.9a    added only the yuke_bpe compression algorithm
22 Nov 2009 - 0.3.9     added two new compressions, fixed a DEP compatibility in the CallDll command and added another path for searching the dll to import, added the possibility of using a variable or memory file in the Encryption command, the SCRIPT MESSAGE alert showed by the Print command is now placed on stderr
12 Nov 2009 - 0.3.8a    fixed a bug in the algorithms used in OpenSSL where the non-padded data was wrongly decrypted at the end, major DEP compatibility with the imported dumped functions (like tzar_lzss, unlz2k and so on), a possible minimal speed/memory improvement and added the compression used in Dungeon Keeper 2
09 Nov 2009 - 0.3.8     added tons of new compression algorithms included the conversion of any base (like base2/binary, base8, base32 and so on), better disposition of the source code, implemented the scanning feature for all the compression algorithms
04 Nov 2009 - 0.3.7     added various compression algorithms (like deflate64 and ppmd) and some usual small enhancements/fixes
31 Oct 2009 - 0.3.6     fixed and optimized the scanning of the input folder, added the manual choice of an alternative filename if the one got from the archives can't be saved and added a couple of non-important compressions and encryptions
29 Oct 2009 - 0.3.5a    better support of files bigger than 2 gigabytes
27 Oct 2009 - 0.3.5     added the ICE encryption, uuencode, ascii86, yenc, compression used in the games of 49Games, mszh, the fullname data type, changed the behaviour of the not and negation operators in Math since now it's taken var2 as input (so now are used both the variables) and added the N (negative) and A (absolute) operators
20 Oct 2009 - 0.3.4b    added only the simple compression algorithm used in Dark Sector (use zip.bms to extract its files)
18 Oct 2009 - 0.3.4a    substituited the strdup function due to its different behaviour on *nix and enabled the usage of lz2k also on *nix
17 Oct 2009 - 0.3.4     added support for plugins and/or custom functions through the CallDLL command, added the unmeng and unlz2k compressions, added a fake 64bit type of variable (fake because QuickBMS works only with 32bit numbers)
12 Oct 2009 - 0.3.3     implemented all the encryption functions of OpenSSL through its great EVP interface, added some new compression algorithms, added some enhancements like the configuration of the lzss parameters and PutVarChr which auto-reallocate the variables
07 Oct 2009 - 0.3.2     added the compressions huffboh, ucl/nrv, dmc, uncompress/lzw, lzhuf, lzari, rle7, rle0, rle, added the encryptions twofish, cast5, seed, serpent, aes-cfb128, fixed lzssboh and idea, added a new switch in Encryption for selecting the encryption mode (default is decryption), added the possibility of specifying a dictionary for the lzo and zlib/inflate compressions, added the visualization of the elapsed seconds for the extraction
29 Sep 2009 - 0.3.1     added a check to avoid to use file numbers not opened and added support for other compressions: rlew, lzjb, sfl (block/rle/nulls/bits) and lzma2
28 Sep 2009 - 0.3       added documentation, added xor, rot and charset as parts of the Encryption command, now the input can be both an archive and a folder containing more archives
20 Sep 2009 - 0.2.4a    nothing changed, has been only removed xcompress.h
19 Sep 2009 - 0.2.4     solved a small bug when a compressed file has a size equal to zero, added support for XMemDecompress and some lzw variants, added the idea encryption. released also the script for extracting the files of Need for Speed: Shift
11 Jul 2009 - 0.2.3a    fixed only an error in the rarely used Padding command
06 Jul 2009 - 0.2.3     added support for the bzip2 compression, the line data type and various big speed improvements moreover in the byte-per-byte operations (like getvarchr and putvarchr)
08 Jun 2009 - 0.2.2a    adapted the code for accepting the options when launched in GUI mode
07 Jun 2009 - 0.2.2     added the -L option for placing the list of files in a file, added the -D option for using decimal notation internally, optimized the For command, gained some milliseconds from myitoa, added the experimental commands for implementing recursive functions (Start, Call and EndFunction)
27 May 2009 - 0.2.1     fixed the handling of some numbers (signed integers and shifting are not friends), the letters between ' are now considered numbers (so 'a' is 0x61), now in Get/PutVarChr is possible to specify also the size of the number through an optional parameter and added: LZX, Binary and BaseName types, BytesRead and NotEOF internal variables (they are used in MultiEx), handling of elif and else (very useful), a new optional parameter in FindLoc to avoid to terminate the script if the string is not found, various new String operators, GetArray and PutArray functions which can be used to work on some temporary dynamic arrays (useful in some occasions), QuickBMSver command which allows to specify the minimum version of QuickBMS supported by that script
09 May 2009 - 0.2       tons of speed improvements and memory and code optimizations, added the double console+gui mode (works from command-line if started from the console or with a minimal gui if double-clicked), when the tool asks to overwrite an existent file now is possible to use &quot;a&quot; or &quot;all&quot; for overwriting them automatically, added multiple memory files (MEMORY_FILE, MEMORY_FILE2, MEMORY_FILE3 and so on), added a TEMPORARY_FILE which is nothing else than a file with this name which is saved also in list mode, all numeric variables are saved in hexadecimal, FileXOR and FileROT13 now accepts also a variable as argument, added support for various encryption algorithms (aes, blowfish, des, 3des, rc4 and xtea), added the Print command which allows to show a message at runtime and various other enhancements and fixes other than new BMS examples like the one which acts as base for the games which use the TTARCH format (remember that each game has its own key which must be specified in the script)
03 May 2009 - 0.1.6     added the Append command, some small optimizations in the usage of realloc with the variables and the memory_file, corrected the position offset used in filexor/filerot13 if set to zero
29 Apr 2009 - 0.1.5     added the explode (pkware data compression library), gzip and lzma (included handling of the x86 header and decoder) compressions, the filename of any opened file is saved and can be read from inside the scripts (example: get NAME filename 1), added the Padding command, some small enhancements and bugfixes of the code
27 Apr 2009 - 0.1.4a    solved a bug in the handling of LZO compression, the break command, added the cleaning of the filename to save and the visualization of the source code's line in case of errors
25 Apr 2009 - 0.1.4     this tool is a scripts based files extractor compatible with the <a href="http://wiki.xentax.com/index.php/BMS">BMS</a> language. the idea was born from the need of a simple and fast solution for handling the simple types of archives used by the majority of games without losing time writing a stand-alone tool with tons of C code just for a basic file format
