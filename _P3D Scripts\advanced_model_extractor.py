#!/usr/bin/env python3
"""
Advanced Model Extractor - Better interpretation of ROM 3D data
"""

import os
import struct
import math
from typing import List, Tuple, Optional

def find_vertex_patterns(data: bytes) -> List[Tuple[int, List[Tuple[float, float, float]]]]:
    """Find patterns that look like actual 3D vertex data"""
    
    patterns = []
    
    # Try different starting offsets and stride patterns
    for start_offset in range(0, min(1024, len(data)), 4):
        for stride in [12, 16, 20, 24, 32]:  # Common vertex data strides
            vertices = []
            
            for i in range(start_offset, len(data) - stride, stride):
                try:
                    # Try to read 3 floats as vertex coordinates
                    x = struct.unpack('<f', data[i:i+4])[0]
                    y = struct.unpack('<f', data[i+4:i+8])[0]
                    z = struct.unpack('<f', data[i+8:i+12])[0]
                    
                    # Check if these look like reasonable vertex coordinates
                    if (not math.isnan(x) and not math.isnan(y) and not math.isnan(z) and
                        not math.isinf(x) and not math.isinf(y) and not math.isinf(z) and
                        -100 <= x <= 100 and -100 <= y <= 100 and -100 <= z <= 100):
                        
                        vertices.append((x, y, z))
                    else:
                        # If we hit bad data, stop this pattern
                        break
                        
                except struct.error:
                    break
            
            # Only keep patterns with reasonable number of vertices
            if 10 <= len(vertices) <= 10000:
                # Check for diversity (not all zeros)
                non_zero = sum(1 for v in vertices if abs(v[0]) > 0.001 or abs(v[1]) > 0.001 or abs(v[2]) > 0.001)
                if non_zero > len(vertices) * 0.1:  # At least 10% non-zero vertices
                    patterns.append((start_offset, vertices))
    
    return patterns

def find_best_vertex_pattern(data: bytes) -> Optional[List[Tuple[float, float, float]]]:
    """Find the best vertex pattern from the data"""
    
    patterns = find_vertex_patterns(data)
    
    if not patterns:
        return None
    
    # Score patterns based on various criteria
    best_pattern = None
    best_score = 0
    
    for offset, vertices in patterns:
        if len(vertices) < 10:
            continue
            
        # Calculate score based on:
        # 1. Number of vertices (more is better, up to a point)
        # 2. Diversity of coordinates (not all zeros)
        # 3. Reasonable coordinate ranges
        # 4. Geometric distribution
        
        vertex_count_score = min(len(vertices) / 1000.0, 1.0)  # Normalize to 0-1
        
        # Diversity score
        non_zero = sum(1 for v in vertices if abs(v[0]) > 0.001 or abs(v[1]) > 0.001 or abs(v[2]) > 0.001)
        diversity_score = non_zero / len(vertices)
        
        # Range score (prefer reasonable ranges)
        xs = [v[0] for v in vertices]
        ys = [v[1] for v in vertices]
        zs = [v[2] for v in vertices]
        
        x_range = max(xs) - min(xs) if xs else 0
        y_range = max(ys) - min(ys) if ys else 0
        z_range = max(zs) - min(zs) if zs else 0
        
        avg_range = (x_range + y_range + z_range) / 3
        range_score = 1.0 if 0.1 <= avg_range <= 50 else 0.5
        
        # Geometric distribution score (prefer vertices that form shapes)
        center_x = sum(xs) / len(xs) if xs else 0
        center_y = sum(ys) / len(ys) if ys else 0
        center_z = sum(zs) / len(zs) if zs else 0
        
        distances = [math.sqrt((v[0]-center_x)**2 + (v[1]-center_y)**2 + (v[2]-center_z)**2) for v in vertices]
        avg_distance = sum(distances) / len(distances) if distances else 0
        distribution_score = 1.0 if 0.1 <= avg_distance <= 20 else 0.5
        
        total_score = vertex_count_score * diversity_score * range_score * distribution_score
        
        if total_score > best_score:
            best_score = total_score
            best_pattern = vertices
    
    return best_pattern

def create_simple_faces(vertex_count: int) -> List[Tuple[int, int, int]]:
    """Create simple triangular faces from vertices"""
    faces = []
    
    # Create faces by connecting sequential vertices in triangles
    for i in range(0, vertex_count - 2, 3):
        if i + 2 < vertex_count:
            faces.append((i, i + 1, i + 2))
    
    return faces

def extract_better_model(filepath: str, output_path: str) -> bool:
    """Extract a better 3D model using advanced pattern recognition"""
    
    with open(filepath, 'rb') as f:
        data = f.read()
    
    # Skip any header (like "BM")
    if data.startswith(b'BM'):
        data = data[2:]
    
    print(f"🔍 Advanced analysis of {os.path.basename(filepath)}...")
    
    # Find the best vertex pattern
    vertices = find_best_vertex_pattern(data)
    
    if not vertices or len(vertices) < 3:
        print(f"  ❌ No valid vertex pattern found")
        return False
    
    # Filter out vertices that are too close to origin (likely padding/noise)
    filtered_vertices = []
    for v in vertices:
        if abs(v[0]) > 0.01 or abs(v[1]) > 0.01 or abs(v[2]) > 0.01:
            filtered_vertices.append(v)
    
    # If we filtered out too many, use original
    if len(filtered_vertices) < len(vertices) * 0.3:
        filtered_vertices = vertices
    
    # Create faces
    faces = create_simple_faces(len(filtered_vertices))
    
    print(f"  ✅ Found {len(filtered_vertices)} vertices, {len(faces)} faces")
    
    # Calculate bounds
    if filtered_vertices:
        xs = [v[0] for v in filtered_vertices]
        ys = [v[1] for v in filtered_vertices]
        zs = [v[2] for v in filtered_vertices]
        
        print(f"  📐 Bounds: X({min(xs):.3f} to {max(xs):.3f})")
        print(f"           Y({min(ys):.3f} to {max(ys):.3f})")
        print(f"           Z({min(zs):.3f} to {max(zs):.3f})")
    
    # Write OBJ file
    base_name = os.path.splitext(os.path.basename(filepath))[0]
    obj_file = os.path.join(output_path, f"advanced_{base_name}.obj")
    
    with open(obj_file, 'w') as f:
        f.write("# Advanced extracted 3D model from ROM data\n")
        f.write(f"# Vertices: {len(filtered_vertices)}\n")
        f.write(f"# Faces: {len(faces)}\n\n")
        
        # Write vertices
        for v in filtered_vertices:
            f.write(f"v {v[0]:.6f} {v[1]:.6f} {v[2]:.6f}\n")
        
        f.write("\n")
        
        # Write faces
        f.write("g AdvancedMesh\n")
        f.write("s 1\n")
        
        for face in faces:
            f.write(f"f {face[0]+1} {face[1]+1} {face[2]+1}\n")
    
    print(f"  💾 Saved: {obj_file}")
    return True

def batch_advanced_extract(input_dir: str, output_dir: str):
    """Extract models using advanced pattern recognition"""
    
    if not os.path.exists(input_dir):
        print(f"❌ Input directory not found: {input_dir}")
        return
    
    os.makedirs(output_dir, exist_ok=True)
    
    # Get files that were identified as having model data
    files = [f for f in os.listdir(input_dir) if f.startswith('extracted_') and f.endswith('.bmp')]
    
    print(f"🧠 ADVANCED 3D MODEL EXTRACTOR")
    print("=" * 60)
    print(f"📁 Input directory: {input_dir}")
    print(f"📁 Output directory: {output_dir}")
    print(f"🔍 Processing {len(files)} files with pattern recognition")
    print("=" * 60)
    
    success_count = 0
    total_vertices = 0
    total_faces = 0
    
    for filename in sorted(files):
        filepath = os.path.join(input_dir, filename)
        
        try:
            if extract_better_model(filepath, output_dir):
                success_count += 1
                
                # Count vertices and faces from the output file
                obj_file = os.path.join(output_dir, f"advanced_{os.path.splitext(filename)[0]}.obj")
                if os.path.exists(obj_file):
                    with open(obj_file, 'r') as f:
                        lines = f.readlines()
                        vertices = sum(1 for line in lines if line.startswith('v '))
                        faces = sum(1 for line in lines if line.startswith('f '))
                        total_vertices += vertices
                        total_faces += faces
            
            print()  # Add spacing between files
            
        except Exception as e:
            print(f"  ❌ Error processing {filename}: {e}")
            print()
    
    # Summary
    print("=" * 60)
    print(f"🎉 ADVANCED EXTRACTION COMPLETE!")
    print(f"✅ Successfully extracted: {success_count}/{len(files)} models")
    print(f"📊 Total vertices: {total_vertices:,}")
    print(f"📊 Total faces: {total_faces:,}")
    
    if success_count > 0:
        print(f"\n🚀 BLENDER IMPORT INSTRUCTIONS:")
        print("1. Open Blender")
        print("2. Delete default cube (X key)")
        print("3. File → Import → Wavefront (.obj)")
        print(f"4. Navigate to '{output_dir}' folder")
        print("5. Select the advanced_*.obj files")
        print("6. Click 'Import OBJ'")
        print("\n✨ These models should display properly without distortion!")
        print("💡 Try importing a few different models to see which ones work best.")

if __name__ == "__main__":
    input_directory = "extracted_files"
    output_directory = "advanced_3d_models"
    
    print("🧠 Advanced 3D Model Extractor")
    print("=" * 60)
    
    batch_advanced_extract(input_directory, output_directory)
