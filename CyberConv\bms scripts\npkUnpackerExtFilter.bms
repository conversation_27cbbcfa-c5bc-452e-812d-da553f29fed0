# NetEase NPK Nameless Assets Unpacker with Extension Filtering by Bigchillghost
# script for QuickBMS http://quickbms.aluigi.org

idstring "NXPK"

get FolderName basename
get FileNum long
goto 0xC 0 SEEK_CUR
get EntryOffset long
goto EntryOffset

set OutName string ""
for i = 0 < FileNum
	get Skip long
	get Offset long
	get ZSize long
	get Size long
	goto 8 0 SEEK_CUR
	get Compression short
	get isEncrypted short
	
	if isEncrypted == 0
		string OutName p "%s\\%08d." FolderName i
		if Compression == 1
			comtype zlib
			clog MEMORY_FILE Offset ZSize Size
		elif Compression == 2
			comtype lz4
			clog MEMORY_FILE Offset ZSize Size
		elif Compression == 3
			comtype oodle
			clog MEMORY_FILE Offset ZSize Size
		else
			log MEMORY_FILE Offset ZSize
		endif
		if Size > 4
			get Magic long MEMORY_FILE
			if Magic == 0x474E5089
				string OutName + "png"
			elif Magic == 0x20534444
				string OutName + "dds"
			elif Magic == 0xBBC88034
				string OutName + "mesh"
			elif Magic == 0x58544BAB
				string OutName + "ktx"
			else
				string OutName + "junk"
			endif
			log OutName 0 Size MEMORY_FILE
		else
			string OutName + "junk"
			log MEMORY_FILE 0 Size
		endif
	endif
next i
