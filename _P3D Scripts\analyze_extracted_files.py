#!/usr/bin/env python3
"""
Analyze the extracted files to determine their actual format
"""

import os
import struct
import binascii

def analyze_file_headers(directory: str):
    """Analyze the headers of extracted files to determine their actual format"""
    
    if not os.path.exists(directory):
        print(f"❌ Directory not found: {directory}")
        return
    
    files = [f for f in os.listdir(directory) if f.startswith('extracted_')]
    files.sort()
    
    print(f"🔍 ANALYZING {len(files)} EXTRACTED FILES")
    print("=" * 80)
    
    # Group files by their actual signatures
    file_types = {}
    
    for filename in files:
        filepath = os.path.join(directory, filename)
        
        try:
            with open(filepath, 'rb') as f:
                data = f.read(64)  # Read first 64 bytes
            
            file_size = os.path.getsize(filepath)
            
            # Analyze the header
            if len(data) >= 4:
                header_hex = data[:16].hex()
                header_ascii = ''.join(chr(b) if 32 <= b <= 126 else '.' for b in data[:16])
                
                # Try to identify the actual format
                actual_format = identify_format(data)
                
                if actual_format not in file_types:
                    file_types[actual_format] = []
                
                file_types[actual_format].append({
                    'filename': filename,
                    'size': file_size,
                    'header_hex': header_hex,
                    'header_ascii': header_ascii,
                    'data_preview': data
                })
                
        except Exception as e:
            print(f"❌ Error analyzing {filename}: {e}")
    
    # Display results grouped by format
    for format_type, files_list in file_types.items():
        print(f"\n📁 {format_type.upper()} FILES ({len(files_list)} files):")
        print("-" * 60)
        
        # Show first few examples
        for i, file_info in enumerate(files_list[:5]):
            print(f"  {file_info['filename']} ({file_info['size']:,} bytes)")
            print(f"    Hex: {file_info['header_hex']}")
            print(f"    ASCII: {file_info['header_ascii']}")
            
            # Additional analysis based on format
            if format_type == "texture_data":
                analyze_texture_data(file_info['data_preview'], file_info['size'])
            elif format_type == "model_data":
                analyze_model_data(file_info['data_preview'])
            
            print()
        
        if len(files_list) > 5:
            print(f"    ... and {len(files_list) - 5} more files")
        print()

def identify_format(data: bytes) -> str:
    """Try to identify the actual format of the data"""
    
    if len(data) < 4:
        return "unknown"
    
    # Check for actual image formats
    if data.startswith(b'\x89PNG'):
        return "png_image"
    elif data.startswith(b'\xff\xd8\xff'):
        return "jpeg_image"
    elif data.startswith(b'GIF8'):
        return "gif_image"
    elif data.startswith(b'BM') and len(data) >= 14:
        # Check if it's a real BMP by validating the header
        try:
            file_size = struct.unpack('<I', data[2:6])[0]
            data_offset = struct.unpack('<I', data[10:14])[0]
            if data_offset > 14 and data_offset < len(data):
                return "bmp_image"
        except:
            pass
    
    # Check for common game data formats
    if data[:4] == b'DDS ':
        return "dds_texture"
    elif data[:4] == b'RIFF':
        return "riff_data"
    elif data[:3] == b'P3D':
        return "p3d_model"
    
    # Analyze data patterns to guess format
    null_count = data[:64].count(0)
    if null_count > 32:  # More than half nulls
        return "sparse_data"
    
    # Check if it looks like texture data (common patterns)
    if len(data) >= 16:
        # Look for power-of-2 dimensions (common in textures)
        for i in range(4, min(16, len(data) - 4), 4):
            try:
                width = struct.unpack('<I', data[i:i+4])[0]
                height = struct.unpack('<I', data[i+4:i+8])[0] if i+8 <= len(data) else 0
                
                if is_power_of_2(width) and is_power_of_2(height) and 16 <= width <= 2048 and 16 <= height <= 2048:
                    return "texture_data"
            except:
                continue
    
    # Check if it looks like 3D model data (lots of floats)
    float_count = 0
    for i in range(0, min(64, len(data) - 4), 4):
        try:
            value = struct.unpack('<f', data[i:i+4])[0]
            if -1000.0 <= value <= 1000.0:  # Reasonable range for model coordinates
                float_count += 1
        except:
            continue
    
    if float_count > 8:
        return "model_data"
    
    # Check for raw pixel data
    if len(data) in [65536, 262144, 1048576]:  # Common texture sizes (256x256, 512x512, 1024x1024 RGBA)
        return "raw_texture"
    
    return "unknown_data"

def is_power_of_2(n):
    """Check if a number is a power of 2"""
    return n > 0 and (n & (n - 1)) == 0

def analyze_texture_data(data: bytes, file_size: int):
    """Analyze potential texture data"""
    print(f"      Possible texture dimensions:")
    
    # Common texture formats and their byte sizes
    formats = [
        ("RGBA32", 4),
        ("RGB24", 3),
        ("RGBA16", 2),
        ("Grayscale", 1),
    ]
    
    for format_name, bytes_per_pixel in formats:
        pixels = file_size // bytes_per_pixel
        
        # Check common square dimensions
        for size in [16, 32, 64, 128, 256, 512, 1024]:
            if pixels == size * size:
                print(f"        {size}x{size} {format_name}")
        
        # Check common rectangular dimensions
        for width in [256, 512, 1024]:
            for height in [128, 256, 512]:
                if pixels == width * height:
                    print(f"        {width}x{height} {format_name}")

def analyze_model_data(data: bytes):
    """Analyze potential 3D model data"""
    print(f"      Potential 3D data:")
    
    # Look for vertex-like data (groups of 3 floats)
    vertex_count = 0
    for i in range(0, min(48, len(data) - 12), 12):
        try:
            x, y, z = struct.unpack('<fff', data[i:i+12])
            if all(-1000 <= coord <= 1000 for coord in [x, y, z]):
                vertex_count += 1
        except:
            continue
    
    if vertex_count > 0:
        print(f"        ~{vertex_count} potential vertices found")

def extract_valid_images(directory: str):
    """Try to extract any valid image data using different approaches"""
    
    print(f"\n🎨 ATTEMPTING ADVANCED IMAGE EXTRACTION")
    print("=" * 80)
    
    files = [f for f in os.listdir(directory) if f.startswith('extracted_')]
    
    # Try to extract raw texture data
    output_dir = os.path.join(directory, "raw_textures")
    os.makedirs(output_dir, exist_ok=True)
    
    extracted_count = 0
    
    for filename in files:
        filepath = os.path.join(directory, filename)
        file_size = os.path.getsize(filepath)
        
        # Try common texture sizes
        texture_configs = [
            (256, 256, 4, "RGBA"),
            (512, 512, 4, "RGBA"),
            (256, 256, 3, "RGB"),
            (512, 512, 3, "RGB"),
            (128, 128, 4, "RGBA"),
            (64, 64, 4, "RGBA"),
        ]
        
        for width, height, channels, format_name in texture_configs:
            expected_size = width * height * channels
            
            if file_size >= expected_size:
                try:
                    with open(filepath, 'rb') as f:
                        raw_data = f.read(expected_size)
                    
                    # Try to create image from raw data
                    from PIL import Image
                    
                    if channels == 4:
                        img = Image.frombytes('RGBA', (width, height), raw_data)
                    elif channels == 3:
                        img = Image.frombytes('RGB', (width, height), raw_data)
                    else:
                        continue
                    
                    # Save as PNG
                    output_filename = f"{filename}_{width}x{height}_{format_name}.png"
                    output_path = os.path.join(output_dir, output_filename)
                    img.save(output_path)
                    
                    print(f"✅ Extracted texture: {output_filename}")
                    extracted_count += 1
                    break  # Only save first successful extraction per file
                    
                except Exception as e:
                    continue
    
    print(f"\n🎉 Extracted {extracted_count} potential textures to {output_dir}")

if __name__ == "__main__":
    directory = "extracted_files"
    
    print("🔍 Advanced File Format Analyzer")
    print("=" * 80)
    
    # Analyze file headers
    analyze_file_headers(directory)
    
    # Try to extract valid images
    try:
        extract_valid_images(directory)
    except ImportError:
        print("⚠️  PIL not available for texture extraction")
    except Exception as e:
        print(f"❌ Error during texture extraction: {e}")
