#!/usr/bin/env python3
"""
Analyze the .X file format to understand the proprietary structure
"""

import os
import struct

def analyze_file_header(file_path, num_bytes=512):
    """Analyze the first few bytes of a file to understand its format"""
    
    print(f"\n🔍 ANALYZING: {os.path.basename(file_path)}")
    print("-" * 60)
    
    try:
        with open(file_path, 'rb') as f:
            data = f.read(num_bytes)
            
        file_size = os.path.getsize(file_path)
        print(f"📊 File size: {file_size:,} bytes")
        
        # Show hex dump of first 128 bytes
        print(f"\n📋 HEX DUMP (first 128 bytes):")
        for i in range(0, min(128, len(data)), 16):
            hex_part = ' '.join(f'{b:02x}' for b in data[i:i+16])
            ascii_part = ''.join(chr(b) if 32 <= b <= 126 else '.' for b in data[i:i+16])
            print(f"  {i:04x}: {hex_part:<48} |{ascii_part}|")
        
        # Look for common file signatures
        print(f"\n🔍 SIGNATURE ANALYSIS:")
        
        # Check for standard DirectX header
        if data.startswith(b'xof '):
            print("   ✅ Standard DirectX .X file detected")
        else:
            print("   ❌ NOT a standard DirectX .X file")
            
        # Check for other common formats
        if data.startswith(b'RIFF'):
            print("   🎵 RIFF format (possibly audio/video)")
        elif data.startswith(b'PK'):
            print("   📦 ZIP/archive format")
        elif data.startswith(b'\x89PNG'):
            print("   🖼️ PNG image")
        elif data.startswith(b'GIF'):
            print("   🖼️ GIF image")
        elif data.startswith(b'\xff\xd8\xff'):
            print("   🖼️ JPEG image")
        elif data.startswith(b'BM'):
            print("   🖼️ BMP image")
        elif data.startswith(b'FBX'):
            print("   🎮 FBX model file")
        elif data.startswith(b'glTF'):
            print("   🎮 glTF model file")
        else:
            print("   ❓ Unknown/proprietary format")
            
        # Look for text patterns
        try:
            text_data = data.decode('utf-8', errors='ignore')
            if 'mesh' in text_data.lower():
                print("   🎮 Contains 'mesh' keyword")
            if 'vertex' in text_data.lower():
                print("   🎮 Contains 'vertex' keyword")
            if 'texture' in text_data.lower():
                print("   🎨 Contains 'texture' keyword")
        except:
            pass
            
        # Check for binary patterns
        print(f"\n📊 BINARY ANALYSIS:")
        
        # Look for potential magic numbers
        if len(data) >= 4:
            magic = struct.unpack('<I', data[:4])[0]
            print(f"   First 4 bytes as uint32: 0x{magic:08x} ({magic})")
            
        if len(data) >= 8:
            magic64 = struct.unpack('<Q', data[:8])[0]
            print(f"   First 8 bytes as uint64: 0x{magic64:016x}")
            
        # Count null bytes (binary indicator)
        null_count = data.count(0)
        null_percentage = (null_count / len(data)) * 100
        print(f"   Null bytes: {null_count}/{len(data)} ({null_percentage:.1f}%)")
        
        if null_percentage > 10:
            print("   📊 Likely binary format")
        else:
            print("   📝 Likely text format")
            
        return data
        
    except Exception as e:
        print(f"❌ Error analyzing {file_path}: {e}")
        return None

def main():
    print("🔬 PROPRIETARY .X FILE FORMAT ANALYZER")
    print("=" * 70)
    
    models_dir = "models_for_blender"
    
    if not os.path.exists(models_dir):
        print(f"❌ Models directory not found: {models_dir}")
        return
    
    # Get .X files sorted by size
    x_files = []
    for file in os.listdir(models_dir):
        if file.lower().endswith('.x'):
            file_path = os.path.join(models_dir, file)
            size = os.path.getsize(file_path)
            x_files.append((file, size))
    
    x_files.sort(key=lambda x: x[1])  # Sort by size, smallest first
    
    print(f"📁 Found {len(x_files)} .X files")
    
    # Analyze a few different sized files
    test_files = []
    
    # Smallest file
    if x_files:
        test_files.append(x_files[0])
    
    # Medium file
    if len(x_files) > len(x_files)//2:
        test_files.append(x_files[len(x_files)//2])
    
    # Largest file
    if len(x_files) > 1:
        test_files.append(x_files[-1])
    
    # Analyze each test file
    for file_name, size in test_files:
        file_path = os.path.join(models_dir, file_name)
        data = analyze_file_header(file_path)
        
        if data:
            print(f"\n💡 CONVERSION SUGGESTIONS:")
            print(f"   • This appears to be a proprietary .X format")
            print(f"   • Standard DirectX importers won't work")
            print(f"   • Need a custom converter or different extraction method")
            print(f"   • Try extracting data009.kom for different formats")
    
    print(f"\n🚀 NEXT STEPS:")
    print(f"   1. Extract data009.kom (our third high-scoring candidate)")
    print(f"   2. Look for different model formats (.obj, .fbx, .mdl)")
    print(f"   3. Try a different extraction approach")
    print(f"   4. Research Elsword-specific model converters")

if __name__ == "__main__":
    main()
