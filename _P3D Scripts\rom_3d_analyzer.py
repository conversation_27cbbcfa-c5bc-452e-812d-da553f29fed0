#!/usr/bin/env python3
"""
ROM 3D Model Analyzer - Analyze ROM file structure to find real 3D model data
"""

import os
import struct
import math
from typing import List, Tuple, Optional, Dict

def analyze_rom_structure(rom_path: str):
    """Analyze the ROM file to understand its structure"""
    
    print(f"🔍 ANALYZING ROM STRUCTURE: {os.path.basename(rom_path)}")
    print("=" * 60)
    
    with open(rom_path, 'rb') as f:
        data = f.read()
    
    file_size = len(data)
    print(f"📊 File size: {file_size:,} bytes ({file_size/1024/1024:.2f} MB)")
    
    # Look for common 3D model file signatures
    signatures = {
        b'MDLX': 'Warcraft III Model',
        b'MD2\x08': 'Quake II Model',
        b'MD3\x0F': 'Quake III Model', 
        b'IDP2': 'Quake II Model',
        b'IDP3': 'Quake III Model',
        b'RASP': 'Rasp Model',
        b'3DMF': '3D Metafile',
        b'FORM': 'IFF/FORM Container',
        b'RIFF': 'RIFF Container',
        b'OBJ\x00': 'OBJ Model',
        b'PLY\x0A': 'PLY Model',
        b'STL\x00': 'STL Model',
        b'MESH': 'Generic Mesh',
        b'VERT': 'Vertex Data',
        b'FACE': 'Face Data',
        b'POLY': 'Polygon Data',
        b'GEOM': 'Geometry Data',
    }
    
    print("🔍 Searching for 3D model signatures...")
    found_signatures = []
    
    for sig, desc in signatures.items():
        pos = 0
        count = 0
        positions = []
        
        while pos < len(data) - len(sig):
            pos = data.find(sig, pos)
            if pos == -1:
                break
            positions.append(pos)
            count += 1
            pos += 1
            
            if count > 100:  # Limit to avoid spam
                break
        
        if count > 0:
            found_signatures.append((sig, desc, count, positions[:10]))
            print(f"  ✅ {desc}: {count} occurrences")
            if count <= 10:
                for p in positions:
                    print(f"     Position: 0x{p:08X} ({p})")
    
    if not found_signatures:
        print("  ❌ No known 3D model signatures found")
    
    # Look for potential vertex data patterns
    print("\n🔍 Analyzing potential vertex data patterns...")
    
    # Look for sequences of floats that could be vertices
    vertex_patterns = find_vertex_sequences(data)
    
    if vertex_patterns:
        print(f"  ✅ Found {len(vertex_patterns)} potential vertex data sections")
        for i, (offset, vertices, quality) in enumerate(vertex_patterns[:5]):
            print(f"     Pattern {i+1}: Offset 0x{offset:08X}, {len(vertices)} vertices, Quality: {quality:.2f}")
            if vertices:
                print(f"       Sample: ({vertices[0][0]:.3f}, {vertices[0][1]:.3f}, {vertices[0][2]:.3f})")
    else:
        print("  ❌ No clear vertex patterns found")
    
    # Look for potential face/index data
    print("\n🔍 Analyzing potential face/index data...")
    
    face_patterns = find_face_sequences(data, len(vertex_patterns[0][1]) if vertex_patterns else 1000)
    
    if face_patterns:
        print(f"  ✅ Found {len(face_patterns)} potential face data sections")
        for i, (offset, faces, format_type) in enumerate(face_patterns[:3]):
            print(f"     Pattern {i+1}: Offset 0x{offset:08X}, {len(faces)} faces, Format: {format_type}")
    else:
        print("  ❌ No clear face patterns found")
    
    return vertex_patterns, face_patterns

def find_vertex_sequences(data: bytes) -> List[Tuple[int, List[Tuple[float, float, float]], float]]:
    """Find sequences that look like vertex data"""
    
    patterns = []
    
    # Try different alignments and strides
    for alignment in [4, 8, 16]:
        for stride in [12, 16, 20, 24, 32]:
            for start in range(0, min(4096, len(data)), alignment):
                vertices = []
                
                for i in range(start, len(data) - stride, stride):
                    try:
                        # Try little-endian floats
                        x = struct.unpack('<f', data[i:i+4])[0]
                        y = struct.unpack('<f', data[i+4:i+8])[0] 
                        z = struct.unpack('<f', data[i+8:i+12])[0]
                        
                        # Check if these are reasonable coordinates
                        if (not any(math.isnan(v) or math.isinf(v) for v in [x, y, z]) and
                            all(-1000 <= v <= 1000 for v in [x, y, z])):
                            vertices.append((x, y, z))
                        else:
                            break
                            
                    except struct.error:
                        break
                
                # Evaluate quality of this vertex sequence
                if len(vertices) >= 20:
                    quality = evaluate_vertex_quality(vertices)
                    if quality > 0.3:  # Minimum quality threshold
                        patterns.append((start, vertices, quality))
    
    # Sort by quality and remove duplicates
    patterns.sort(key=lambda x: x[2], reverse=True)
    
    # Remove similar patterns (same starting area)
    unique_patterns = []
    for pattern in patterns:
        is_unique = True
        for existing in unique_patterns:
            if abs(pattern[0] - existing[0]) < 1000:  # Too close to existing pattern
                is_unique = False
                break
        if is_unique:
            unique_patterns.append(pattern)
    
    return unique_patterns[:10]  # Return top 10

def evaluate_vertex_quality(vertices: List[Tuple[float, float, float]]) -> float:
    """Evaluate how likely these vertices represent real 3D model data"""
    
    if len(vertices) < 3:
        return 0.0
    
    score = 0.0
    
    # 1. Diversity score (not all zeros)
    non_zero = sum(1 for v in vertices if any(abs(coord) > 0.001 for coord in v))
    diversity_score = non_zero / len(vertices)
    score += diversity_score * 0.3
    
    # 2. Range score (reasonable coordinate ranges)
    xs = [v[0] for v in vertices]
    ys = [v[1] for v in vertices]
    zs = [v[2] for v in vertices]
    
    ranges = [max(coords) - min(coords) for coords in [xs, ys, zs]]
    avg_range = sum(ranges) / 3
    
    if 0.1 <= avg_range <= 100:
        score += 0.3
    elif avg_range > 0.001:
        score += 0.1
    
    # 3. Distribution score (vertices form reasonable shapes)
    if len(vertices) >= 10:
        center_x = sum(xs) / len(xs)
        center_y = sum(ys) / len(ys)
        center_z = sum(zs) / len(zs)
        
        distances = [math.sqrt((v[0]-center_x)**2 + (v[1]-center_y)**2 + (v[2]-center_z)**2) 
                    for v in vertices]
        
        if distances:
            avg_dist = sum(distances) / len(distances)
            std_dev = math.sqrt(sum((d - avg_dist)**2 for d in distances) / len(distances))
            
            if 0.1 <= avg_dist <= 50 and std_dev > 0.01:
                score += 0.2
    
    # 4. Geometric consistency (vertices could form triangles)
    if len(vertices) >= 9:
        # Check if we can form reasonable triangles
        triangle_score = 0
        for i in range(0, min(len(vertices)-2, 30), 3):
            v1, v2, v3 = vertices[i], vertices[i+1], vertices[i+2]
            
            # Calculate triangle area
            edge1 = (v2[0]-v1[0], v2[1]-v1[1], v2[2]-v1[2])
            edge2 = (v3[0]-v1[0], v3[1]-v1[1], v3[2]-v1[2])
            
            # Cross product for area
            cross = (
                edge1[1]*edge2[2] - edge1[2]*edge2[1],
                edge1[2]*edge2[0] - edge1[0]*edge2[2], 
                edge1[0]*edge2[1] - edge1[1]*edge2[0]
            )
            
            area = math.sqrt(cross[0]**2 + cross[1]**2 + cross[2]**2) / 2
            
            if 0.001 <= area <= 100:  # Reasonable triangle area
                triangle_score += 1
        
        if triangle_score > 0:
            score += min(triangle_score / 10, 0.2)
    
    return min(score, 1.0)

def find_face_sequences(data: bytes, max_vertex_index: int) -> List[Tuple[int, List, str]]:
    """Find sequences that look like face/index data"""
    
    patterns = []
    
    # Try different index formats
    for start in range(0, min(4096, len(data)), 4):
        
        # Try 16-bit indices (triangles)
        faces_16 = []
        for i in range(start, len(data) - 6, 6):
            try:
                idx1 = struct.unpack('<H', data[i:i+2])[0]
                idx2 = struct.unpack('<H', data[i+2:i+4])[0]
                idx3 = struct.unpack('<H', data[i+4:i+6])[0]
                
                if (0 < idx1 <= max_vertex_index and 
                    0 < idx2 <= max_vertex_index and 
                    0 < idx3 <= max_vertex_index and
                    idx1 != idx2 and idx2 != idx3 and idx1 != idx3):
                    faces_16.append((idx1, idx2, idx3))
                else:
                    break
            except struct.error:
                break
        
        if len(faces_16) >= 10:
            patterns.append((start, faces_16, "16-bit triangles"))
        
        # Try 32-bit indices (triangles)
        faces_32 = []
        for i in range(start, len(data) - 12, 12):
            try:
                idx1 = struct.unpack('<I', data[i:i+4])[0]
                idx2 = struct.unpack('<I', data[i+4:i+8])[0]
                idx3 = struct.unpack('<I', data[i+8:i+12])[0]
                
                if (0 < idx1 <= max_vertex_index and 
                    0 < idx2 <= max_vertex_index and 
                    0 < idx3 <= max_vertex_index and
                    idx1 != idx2 and idx2 != idx3 and idx1 != idx3):
                    faces_32.append((idx1, idx2, idx3))
                else:
                    break
            except struct.error:
                break
        
        if len(faces_32) >= 10:
            patterns.append((start, faces_32, "32-bit triangles"))
    
    return patterns[:5]  # Return top 5

def extract_model_from_rom(rom_path: str, output_dir: str):
    """Extract 3D models from ROM using structural analysis"""
    
    vertex_patterns, face_patterns = analyze_rom_structure(rom_path)
    
    if not vertex_patterns:
        print("\n❌ No suitable vertex data found in ROM")
        return
    
    os.makedirs(output_dir, exist_ok=True)
    
    print(f"\n🚀 EXTRACTING MODELS TO: {output_dir}")
    print("=" * 60)
    
    for i, (v_offset, vertices, quality) in enumerate(vertex_patterns[:5]):
        print(f"\n📦 Extracting Model {i+1}...")
        print(f"   Vertex offset: 0x{v_offset:08X}")
        print(f"   Vertices: {len(vertices)}")
        print(f"   Quality: {quality:.2f}")
        
        # Find matching face data
        faces = []
        face_format = "generated"
        
        for f_offset, face_data, fmt in face_patterns:
            if abs(f_offset - v_offset) < 10000:  # Face data near vertex data
                faces = [(f[0]-1, f[1]-1, f[2]-1) for f in face_data if all(0 <= idx < len(vertices) for idx in f)]
                face_format = fmt
                break
        
        # Generate faces if none found
        if not faces:
            faces = [(j, j+1, j+2) for j in range(0, len(vertices)-2, 3)]
            face_format = "auto-generated triangles"
        
        print(f"   Faces: {len(faces)} ({face_format})")
        
        # Write OBJ file
        obj_path = os.path.join(output_dir, f"rom_model_{i+1:03d}.obj")
        
        with open(obj_path, 'w') as f:
            f.write(f"# ROM 3D Model {i+1}\n")
            f.write(f"# Extracted from offset 0x{v_offset:08X}\n")
            f.write(f"# Quality score: {quality:.2f}\n")
            f.write(f"# Vertices: {len(vertices)}, Faces: {len(faces)}\n\n")
            
            # Write vertices
            for v in vertices:
                f.write(f"v {v[0]:.6f} {v[1]:.6f} {v[2]:.6f}\n")
            
            f.write("\n")
            
            # Write faces
            f.write(f"g ROMModel{i+1}\n")
            f.write("s 1\n")
            
            for face in faces:
                if all(0 <= idx < len(vertices) for idx in face):
                    f.write(f"f {face[0]+1} {face[1]+1} {face[2]+1}\n")
        
        print(f"   💾 Saved: {obj_path}")
    
    print(f"\n🎉 Extraction complete! {len(vertex_patterns)} models extracted.")
    print(f"\n🚀 BLENDER IMPORT INSTRUCTIONS:")
    print("1. Open Blender")
    print("2. Delete default cube (X key)")
    print("3. File → Import → Wavefront (.obj)")
    print(f"4. Navigate to '{output_dir}' folder")
    print("5. Select rom_model_*.obj files")
    print("6. Click 'Import OBJ'")
    print("\n✨ These models should show proper 3D geometry!")

if __name__ == "__main__":
    # Look for ROM file in current directory
    rom_files = [f for f in os.listdir('.') if f.lower().endswith(('.rom', '.bin', '.n64', '.z64', '.v64', '.gba', '.nds', '.3ds'))]
    
    if not rom_files:
        print("❌ No ROM file found!")
        print("💡 Please place your ROM file (.rom, .bin, .n64, .z64, etc.) in this folder")
        print("   Common ROM extensions: .rom, .bin, .n64, .z64, .v64, .gba, .nds, .3ds")
    else:
        rom_file = rom_files[0]
        print(f"🎮 Found ROM file: {rom_file}")
        extract_model_from_rom(rom_file, "rom_extracted_models")
