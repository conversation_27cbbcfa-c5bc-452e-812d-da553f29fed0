#!/usr/bin/env python3
"""
KSM to OBJ Converter for Elsword Models
Attempts to parse the proprietary KSM format and convert to OBJ
"""

import os
import struct
import math

class KSMParser:
    def __init__(self, file_path):
        self.file_path = file_path
        self.data = None
        self.vertices = []
        self.faces = []
        self.normals = []
        self.uvs = []
        
    def load_file(self):
        """Load the KSM file into memory"""
        try:
            with open(self.file_path, 'rb') as f:
                self.data = f.read()
            return True
        except Exception as e:
            print(f"❌ Error loading file: {e}")
            return False
    
    def parse_header(self):
        """Parse the KSM header"""
        if len(self.data) < 32:
            return False
            
        # Check signature
        signature = self.data[:4]
        if signature != b'KSM ':
            print(f"❌ Invalid signature: {signature}")
            return False
            
        # Parse header
        version = struct.unpack('<I', self.data[4:8])[0]
        data_size = struct.unpack('<I', self.data[8:12])[0]
        
        print(f"✅ KSM Header:")
        print(f"   Version: {version}")
        print(f"   Data size: {data_size:,} bytes")
        
        # Look for bounding box (floats after header)
        bbox_start = 12
        if len(self.data) >= bbox_start + 24:  # 6 floats = 24 bytes
            bbox = struct.unpack('<6f', self.data[bbox_start:bbox_start+24])
            print(f"   Bounding box: {bbox}")
            
        return True
    
    def find_vertex_data(self):
        """Attempt to find vertex data in the file"""
        print(f"\n🔍 Searching for vertex data...")
        
        # Look for patterns that might be vertices
        # Vertices are typically groups of 3 floats (x, y, z)
        vertex_candidates = []
        
        # Scan through the file looking for float patterns
        for offset in range(64, len(self.data) - 12, 4):  # Start after header
            try:
                # Try to read 3 consecutive floats
                x, y, z = struct.unpack('<3f', self.data[offset:offset+12])
                
                # Check if these look like reasonable vertex coordinates
                if (abs(x) < 1000 and abs(y) < 1000 and abs(z) < 1000 and
                    not (x == 0 and y == 0 and z == 0) and
                    not math.isnan(x) and not math.isnan(y) and not math.isnan(z)):
                    
                    vertex_candidates.append((offset, x, y, z))
                    
            except struct.error:
                continue
        
        print(f"   Found {len(vertex_candidates)} potential vertices")
        
        # Group consecutive vertices
        if vertex_candidates:
            # Take the first reasonable group
            start_offset = vertex_candidates[0][0]
            consecutive_count = 0
            
            for i in range(len(vertex_candidates) - 1):
                current_offset = vertex_candidates[i][0]
                next_offset = vertex_candidates[i + 1][0]
                
                if next_offset - current_offset == 12:  # 3 floats = 12 bytes
                    consecutive_count += 1
                else:
                    if consecutive_count >= 3:  # At least 3 consecutive vertices
                        break
                    consecutive_count = 0
                    start_offset = next_offset
            
            if consecutive_count >= 3:
                print(f"   Found vertex block at offset {start_offset:x} with {consecutive_count} vertices")
                
                # Extract vertices
                for i in range(consecutive_count + 1):
                    offset = start_offset + (i * 12)
                    if offset + 12 <= len(self.data):
                        x, y, z = struct.unpack('<3f', self.data[offset:offset+12])
                        self.vertices.append((x, y, z))
                
                return True
        
        return False
    
    def find_face_data(self):
        """Attempt to find face/triangle data"""
        print(f"\n🔍 Searching for face data...")
        
        if not self.vertices:
            return False
        
        vertex_count = len(self.vertices)
        print(f"   Vertex count: {vertex_count}")
        
        # Look for triangle indices (groups of 3 integers)
        face_candidates = []
        
        # Scan for potential face indices
        for offset in range(64, len(self.data) - 12, 4):
            try:
                # Try to read 3 consecutive integers
                i1, i2, i3 = struct.unpack('<3I', self.data[offset:offset+12])
                
                # Check if these could be valid vertex indices
                if (i1 < vertex_count and i2 < vertex_count and i3 < vertex_count and
                    i1 != i2 and i2 != i3 and i1 != i3):
                    
                    face_candidates.append((offset, i1, i2, i3))
                    
            except struct.error:
                continue
        
        print(f"   Found {len(face_candidates)} potential faces")
        
        # Take the first reasonable group of faces
        if face_candidates:
            # Limit to reasonable number of faces
            max_faces = min(len(face_candidates), vertex_count * 2)
            
            for i in range(max_faces):
                _, i1, i2, i3 = face_candidates[i]
                self.faces.append((i1, i2, i3))
            
            return True
        
        return False
    
    def write_obj(self, output_path):
        """Write the parsed data to an OBJ file"""
        try:
            with open(output_path, 'w') as f:
                f.write("# Converted from KSM format\n")
                f.write(f"# Source: {os.path.basename(self.file_path)}\n\n")
                
                # Write vertices
                for x, y, z in self.vertices:
                    f.write(f"v {x:.6f} {y:.6f} {z:.6f}\n")
                
                f.write("\n")
                
                # Write faces (OBJ uses 1-based indexing)
                for i1, i2, i3 in self.faces:
                    f.write(f"f {i1+1} {i2+1} {i3+1}\n")
            
            return True
            
        except Exception as e:
            print(f"❌ Error writing OBJ: {e}")
            return False

def convert_ksm_to_obj(ksm_path, obj_path):
    """Convert a single KSM file to OBJ"""
    print(f"\n🔄 Converting: {os.path.basename(ksm_path)}")
    print("-" * 50)
    
    parser = KSMParser(ksm_path)
    
    # Load file
    if not parser.load_file():
        return False
    
    # Parse header
    if not parser.parse_header():
        return False
    
    # Find vertex data
    if not parser.find_vertex_data():
        print("❌ Could not find vertex data")
        return False
    
    # Find face data
    if not parser.find_face_data():
        print("⚠️  Could not find face data, creating point cloud")
    
    # Write OBJ
    if parser.write_obj(obj_path):
        file_size = os.path.getsize(obj_path)
        print(f"✅ Success! Created {os.path.basename(obj_path)} ({file_size:,} bytes)")
        print(f"   Vertices: {len(parser.vertices)}")
        print(f"   Faces: {len(parser.faces)}")
        return True
    
    return False

def main():
    print("🔧 KSM TO OBJ CONVERTER")
    print("=" * 50)
    
    models_dir = "models_for_blender"
    output_dir = "converted_obj_models"
    
    if not os.path.exists(models_dir):
        print(f"❌ Models directory not found: {models_dir}")
        return
    
    # Create output directory
    if os.path.exists(output_dir):
        import shutil
        shutil.rmtree(output_dir)
    os.makedirs(output_dir, exist_ok=True)
    
    # Get .X files (KSM format)
    x_files = [f for f in os.listdir(models_dir) if f.lower().endswith('.x')]
    
    if not x_files:
        print(f"❌ No .X files found in {models_dir}")
        return
    
    print(f"📁 Found {len(x_files)} KSM files to convert")
    
    # Convert files (start with smaller ones)
    x_file_sizes = []
    for x_file in x_files:
        x_path = os.path.join(models_dir, x_file)
        size = os.path.getsize(x_path)
        x_file_sizes.append((x_file, size))
    
    x_file_sizes.sort(key=lambda x: x[1])  # Sort by size
    
    successful_conversions = 0
    failed_conversions = 0
    
    # Try converting first 10 files
    for i, (x_file, size) in enumerate(x_file_sizes[:10]):
        x_path = os.path.join(models_dir, x_file)
        obj_file = os.path.splitext(x_file)[0] + '.obj'
        obj_path = os.path.join(output_dir, obj_file)
        
        print(f"\n📦 [{i+1}/10] {x_file} ({size:,} bytes)")
        
        if convert_ksm_to_obj(x_path, obj_path):
            successful_conversions += 1
        else:
            failed_conversions += 1
    
    # Summary
    print(f"\n🎉 CONVERSION COMPLETE!")
    print("=" * 50)
    print(f"✅ Successful: {successful_conversions}")
    print(f"❌ Failed: {failed_conversions}")
    
    if successful_conversions > 0:
        print(f"\n📁 Converted files are in: {output_dir}/")
        print(f"🚀 Import these OBJ files into Blender:")
        print(f"   File → Import → Wavefront (.obj)")

if __name__ == "__main__":
    main()
