#!/usr/bin/env python3
"""
Model Distortion Fixer - Analyze and fix geometry issues in extracted 3D models
"""

import os
import math
import struct
from typing import List, Tuple, Optional

def analyze_vertex_distribution(obj_file: str):
    """Analyze vertex distribution to identify distortion patterns"""
    
    vertices = []
    
    with open(obj_file, 'r') as f:
        for line in f:
            if line.startswith('v '):
                parts = line.strip().split()
                if len(parts) >= 4:
                    try:
                        x, y, z = float(parts[1]), float(parts[2]), float(parts[3])
                        vertices.append((x, y, z))
                    except ValueError:
                        continue
    
    if not vertices:
        print(f"❌ No vertices found in {obj_file}")
        return None
    
    # Calculate statistics
    xs = [v[0] for v in vertices]
    ys = [v[1] for v in vertices]
    zs = [v[2] for v in vertices]
    
    stats = {
        'vertex_count': len(vertices),
        'x_range': (min(xs), max(xs)),
        'y_range': (min(ys), max(ys)),
        'z_range': (min(zs), max(zs)),
        'x_span': max(xs) - min(xs),
        'y_span': max(ys) - min(ys),
        'z_span': max(zs) - min(zs),
    }
    
    # Check for common distortion patterns
    issues = []
    
    # Check for extreme coordinate values
    max_coord = max(abs(min(xs)), abs(max(xs)), abs(min(ys)), abs(max(ys)), abs(min(zs)), abs(max(zs)))
    if max_coord > 1000:
        issues.append(f"Extreme coordinates detected (max: {max_coord:.2f})")
    
    # Check for very small models
    max_span = max(stats['x_span'], stats['y_span'], stats['z_span'])
    if max_span < 0.01:
        issues.append(f"Model too small (max span: {max_span:.6f})")
    
    # Check for flat models (missing dimension)
    if stats['x_span'] < 0.001:
        issues.append("Model is flat in X dimension")
    if stats['y_span'] < 0.001:
        issues.append("Model is flat in Y dimension")
    if stats['z_span'] < 0.001:
        issues.append("Model is flat in Z dimension")
    
    # Check for clustering around zero
    zero_vertices = sum(1 for v in vertices if abs(v[0]) < 0.001 and abs(v[1]) < 0.001 and abs(v[2]) < 0.001)
    if zero_vertices > len(vertices) * 0.5:
        issues.append(f"Too many vertices at origin ({zero_vertices}/{len(vertices)})")
    
    return stats, issues

def fix_vertex_data_interpretation(input_file: str, output_file: str, fix_type: str = "auto"):
    """Try different interpretations of the vertex data to fix distortion"""
    
    print(f"🔧 Fixing {os.path.basename(input_file)} with method: {fix_type}")
    
    vertices = []
    normals = []
    uvs = []
    faces = []
    other_lines = []
    
    # Read the original file
    with open(input_file, 'r') as f:
        for line in f:
            line = line.strip()
            if line.startswith('v '):
                parts = line.split()
                if len(parts) >= 4:
                    try:
                        x, y, z = float(parts[1]), float(parts[2]), float(parts[3])
                        vertices.append((x, y, z))
                    except ValueError:
                        vertices.append((0.0, 0.0, 0.0))
            elif line.startswith('vn '):
                parts = line.split()
                if len(parts) >= 4:
                    try:
                        x, y, z = float(parts[1]), float(parts[2]), float(parts[3])
                        normals.append((x, y, z))
                    except ValueError:
                        normals.append((0.0, 0.0, 1.0))
            elif line.startswith('vt '):
                parts = line.split()
                if len(parts) >= 3:
                    try:
                        u, v = float(parts[1]), float(parts[2])
                        uvs.append((u, v))
                    except ValueError:
                        uvs.append((0.0, 0.0))
            elif line.startswith('f '):
                faces.append(line)
            else:
                other_lines.append(line)
    
    if not vertices:
        print("  ❌ No vertices to process")
        return False
    
    # Apply fixes based on the method
    fixed_vertices = []
    
    if fix_type == "scale_up":
        # Scale up tiny models
        scale_factor = 100.0
        for x, y, z in vertices:
            fixed_vertices.append((x * scale_factor, y * scale_factor, z * scale_factor))
        print(f"  📏 Scaled up by {scale_factor}x")
    
    elif fix_type == "scale_down":
        # Scale down huge models
        scale_factor = 0.01
        for x, y, z in vertices:
            fixed_vertices.append((x * scale_factor, y * scale_factor, z * scale_factor))
        print(f"  📏 Scaled down by {1/scale_factor}x")
    
    elif fix_type == "normalize":
        # Normalize to unit cube
        xs = [v[0] for v in vertices]
        ys = [v[1] for v in vertices]
        zs = [v[2] for v in vertices]
        
        min_x, max_x = min(xs), max(xs)
        min_y, max_y = min(ys), max(ys)
        min_z, max_z = min(zs), max(zs)
        
        # Center and scale
        center_x = (min_x + max_x) / 2
        center_y = (min_y + max_y) / 2
        center_z = (min_z + max_z) / 2
        
        span_x = max_x - min_x
        span_y = max_y - min_y
        span_z = max_z - min_z
        max_span = max(span_x, span_y, span_z)
        
        if max_span > 0:
            scale = 2.0 / max_span  # Scale to fit in 2x2x2 cube
            for x, y, z in vertices:
                new_x = (x - center_x) * scale
                new_y = (y - center_y) * scale
                new_z = (z - center_z) * scale
                fixed_vertices.append((new_x, new_y, new_z))
        else:
            fixed_vertices = vertices
        print(f"  📐 Normalized to unit cube")
    
    elif fix_type == "reinterpret_coords":
        # Try different coordinate interpretations
        for x, y, z in vertices:
            # Swap Y and Z (common issue between coordinate systems)
            fixed_vertices.append((x, z, -y))
        print(f"  🔄 Swapped Y/Z coordinates")
    
    elif fix_type == "remove_outliers":
        # Remove extreme outlier vertices
        xs = [v[0] for v in vertices]
        ys = [v[1] for v in vertices]
        zs = [v[2] for v in vertices]
        
        # Calculate percentiles
        xs_sorted = sorted(xs)
        ys_sorted = sorted(ys)
        zs_sorted = sorted(zs)
        
        def percentile(data, p):
            k = (len(data) - 1) * p / 100
            f = math.floor(k)
            c = math.ceil(k)
            if f == c:
                return data[int(k)]
            return data[int(f)] * (c - k) + data[int(c)] * (k - f)
        
        x_p1, x_p99 = percentile(xs_sorted, 1), percentile(xs_sorted, 99)
        y_p1, y_p99 = percentile(ys_sorted, 1), percentile(ys_sorted, 99)
        z_p1, z_p99 = percentile(zs_sorted, 1), percentile(zs_sorted, 99)
        
        outliers_removed = 0
        for x, y, z in vertices:
            if (x_p1 <= x <= x_p99 and y_p1 <= y <= y_p99 and z_p1 <= z <= z_p99):
                fixed_vertices.append((x, y, z))
            else:
                fixed_vertices.append((0.0, 0.0, 0.0))  # Replace outliers with origin
                outliers_removed += 1
        
        print(f"  🧹 Removed {outliers_removed} outlier vertices")
    
    elif fix_type == "auto":
        # Automatically detect and apply the best fix
        stats, issues = analyze_vertex_distribution(input_file)
        
        if "too small" in str(issues).lower():
            return fix_vertex_data_interpretation(input_file, output_file, "scale_up")
        elif "extreme coordinates" in str(issues).lower():
            return fix_vertex_data_interpretation(input_file, output_file, "scale_down")
        elif "flat in" in str(issues).lower():
            return fix_vertex_data_interpretation(input_file, output_file, "reinterpret_coords")
        elif "too many vertices at origin" in str(issues).lower():
            return fix_vertex_data_interpretation(input_file, output_file, "remove_outliers")
        else:
            return fix_vertex_data_interpretation(input_file, output_file, "normalize")
    
    else:
        # No fix applied
        fixed_vertices = vertices
    
    # Write the fixed file
    with open(output_file, 'w') as f:
        # Write header
        f.write("# Fixed OBJ file for Blender (distortion corrected)\n")
        f.write(f"# Applied fix: {fix_type}\n")
        f.write(f"# Vertices: {len(fixed_vertices)}\n\n")
        
        # Write other header lines
        for line in other_lines:
            if line.startswith('#') or line.startswith('mtllib') or line.startswith('o ') or line.startswith('g '):
                f.write(line + '\n')
        
        f.write('\n')
        
        # Write fixed vertices
        for x, y, z in fixed_vertices:
            f.write(f"v {x:.6f} {y:.6f} {z:.6f}\n")
        
        f.write('\n')
        
        # Write UVs
        if uvs:
            for u, v in uvs[:len(fixed_vertices)]:
                f.write(f"vt {u:.6f} {v:.6f}\n")
            f.write('\n')
        
        # Write normals
        if normals:
            for x, y, z in normals[:len(fixed_vertices)]:
                # Normalize the normal
                length = math.sqrt(x*x + y*y + z*z)
                if length > 0:
                    x, y, z = x/length, y/length, z/length
                else:
                    x, y, z = 0.0, 0.0, 1.0
                f.write(f"vn {x:.6f} {y:.6f} {z:.6f}\n")
            f.write('\n')
        
        # Write faces
        f.write("s 1\n")  # Smooth shading
        for face_line in faces:
            f.write(face_line + '\n')
    
    print(f"  ✅ Fixed model saved to {output_file}")
    return True

def batch_fix_models(input_dir: str, output_dir: str, fix_method: str = "auto"):
    """Fix all models in a directory"""
    
    if not os.path.exists(input_dir):
        print(f"❌ Input directory not found: {input_dir}")
        return
    
    os.makedirs(output_dir, exist_ok=True)
    
    obj_files = [f for f in os.listdir(input_dir) if f.endswith('.obj')]
    
    if not obj_files:
        print(f"❌ No OBJ files found in {input_dir}")
        return
    
    print(f"🔧 FIXING {len(obj_files)} DISTORTED MODELS")
    print("=" * 60)
    print(f"📁 Input directory: {input_dir}")
    print(f"📁 Output directory: {output_dir}")
    print(f"🛠️  Fix method: {fix_method}")
    print("=" * 60)
    
    fixed_count = 0
    
    for obj_file in sorted(obj_files):
        input_path = os.path.join(input_dir, obj_file)
        output_path = os.path.join(output_dir, f"fixed_{obj_file}")
        
        # Analyze the model first
        print(f"\n📊 Analyzing {obj_file}...")
        result = analyze_vertex_distribution(input_path)
        
        if result:
            stats, issues = result
            print(f"  📐 Bounds: X({stats['x_range'][0]:.3f} to {stats['x_range'][1]:.3f})")
            print(f"           Y({stats['y_range'][0]:.3f} to {stats['y_range'][1]:.3f})")
            print(f"           Z({stats['z_range'][0]:.3f} to {stats['z_range'][1]:.3f})")
            print(f"  📏 Spans: {stats['x_span']:.3f} x {stats['y_span']:.3f} x {stats['z_span']:.3f}")
            
            if issues:
                print(f"  ⚠️  Issues: {', '.join(issues)}")
            else:
                print(f"  ✅ No obvious issues detected")
        
        # Apply fix
        if fix_vertex_data_interpretation(input_path, output_path, fix_method):
            fixed_count += 1
    
    print(f"\n" + "=" * 60)
    print(f"🎉 FIXING COMPLETE!")
    print(f"✅ Successfully fixed: {fixed_count}/{len(obj_files)} models")
    
    if fixed_count > 0:
        print(f"\n🚀 BLENDER IMPORT INSTRUCTIONS:")
        print("1. Open Blender")
        print("2. Delete default cube (X key)")
        print("3. File → Import → Wavefront (.obj)")
        print(f"4. Navigate to '{output_dir}' folder")
        print("5. Select the fixed_*.obj files")
        print("6. Click 'Import OBJ'")
        print("\n✨ The models should now display correctly without distortion!")

if __name__ == "__main__":
    input_directory = "cleaned_3d_models"
    output_directory = "fixed_3d_models"
    
    print("🔧 Model Distortion Fixer")
    print("=" * 60)
    
    # Available fix methods:
    # "auto" - Automatically detect and apply best fix
    # "scale_up" - Scale up tiny models
    # "scale_down" - Scale down huge models  
    # "normalize" - Normalize to unit cube
    # "reinterpret_coords" - Try different coordinate system
    # "remove_outliers" - Remove extreme outlier vertices
    
    batch_fix_models(input_directory, output_directory, "auto")
