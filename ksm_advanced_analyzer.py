#!/usr/bin/env python3
"""
Advanced KSM Format Analysis
Study the exact structure to understand vertex and face data layout
"""

import os
import struct

def analyze_ksm_detailed(file_path):
    """Detailed analysis of KSM structure"""
    
    print(f"\n🔬 DETAILED ANALYSIS: {os.path.basename(file_path)}")
    print("=" * 60)
    
    try:
        with open(file_path, 'rb') as f:
            data = f.read()
    except Exception as e:
        print(f"❌ Error reading file: {e}")
        return
    
    file_size = len(data)
    print(f"📊 File size: {file_size:,} bytes")
    
    # Parse header
    signature = data[:4]
    version = struct.unpack('<I', data[4:8])[0]
    data_size = struct.unpack('<I', data[8:12])[0]
    
    print(f"📋 Header:")
    print(f"   Signature: {signature}")
    print(f"   Version: {version} (0x{version:x})")
    print(f"   Data size: {data_size:,} bytes")
    
    # Look for vertex count indicators
    print(f"\n🔍 Looking for vertex/face counts...")
    
    # Check common locations for counts
    for offset in [12, 16, 20, 24, 28, 32, 36, 40, 44, 48]:
        if offset + 4 <= len(data):
            value = struct.unpack('<I', data[offset:offset+4])[0]
            if 3 <= value <= 10000:  # Reasonable vertex count range
                print(f"   Offset 0x{offset:x}: {value} (potential vertex/face count)")
    
    # Look for data sections with consistent patterns
    print(f"\n📊 Searching for data patterns...")
    
    # Find areas with consistent float patterns
    float_regions = []
    current_region = None
    
    for offset in range(12, len(data) - 12, 4):
        try:
            val = struct.unpack('<f', data[offset:offset+4])[0]
            
            # Check if this looks like a reasonable coordinate
            if abs(val) < 1000 and abs(val) > 0.001 and not (val == 0):
                if current_region is None:
                    current_region = {'start': offset, 'count': 1, 'values': [val]}
                elif offset - current_region['start'] == current_region['count'] * 4:
                    current_region['count'] += 1
                    current_region['values'].append(val)
                else:
                    if current_region['count'] >= 9:  # At least 3 vertices
                        float_regions.append(current_region)
                    current_region = {'start': offset, 'count': 1, 'values': [val]}
            else:
                if current_region and current_region['count'] >= 9:
                    float_regions.append(current_region)
                current_region = None
                
        except struct.error:
            continue
    
    # Add final region
    if current_region and current_region['count'] >= 9:
        float_regions.append(current_region)
    
    print(f"   Found {len(float_regions)} potential vertex regions")
    
    # Analyze the best regions
    for i, region in enumerate(float_regions[:3]):
        print(f"\n🎮 Region {i+1}:")
        print(f"   Offset: 0x{region['start']:x}")
        print(f"   Float count: {region['count']}")
        print(f"   Potential vertices: {region['count'] // 3}")
        
        # Show first few vertices
        values = region['values']
        print(f"   First vertices:")
        for j in range(0, min(15, len(values)), 3):
            if j + 2 < len(values):
                x, y, z = values[j], values[j+1], values[j+2]
                print(f"     Vertex {j//3}: {x:.3f}, {y:.3f}, {z:.3f}")
    
    # Look for index data (integers that could be face indices)
    print(f"\n🔢 Searching for face indices...")
    
    max_vertex_count = max([r['count'] // 3 for r in float_regions]) if float_regions else 0
    
    index_regions = []
    for offset in range(12, len(data) - 12, 4):
        try:
            val = struct.unpack('<I', data[offset:offset+4])[0]
            
            # Check if this could be a vertex index
            if 0 <= val < max_vertex_count:
                # Check if next two values are also valid indices
                if offset + 8 <= len(data):
                    val2 = struct.unpack('<I', data[offset+4:offset+8])[0]
                    val3 = struct.unpack('<I', data[offset+8:offset+12])[0]
                    
                    if (0 <= val2 < max_vertex_count and 0 <= val3 < max_vertex_count and
                        val != val2 and val2 != val3 and val != val3):
                        
                        index_regions.append({
                            'offset': offset,
                            'indices': [val, val2, val3]
                        })
                        
        except struct.error:
            continue
    
    print(f"   Found {len(index_regions)} potential face triangles")
    
    if index_regions:
        print(f"   First few faces:")
        for i, region in enumerate(index_regions[:5]):
            indices = region['indices']
            print(f"     Face {i}: {indices[0]}, {indices[1]}, {indices[2]}")
    
    # Look for specific data block markers
    print(f"\n🏷️  Searching for data markers...")
    
    # Common 3D format markers
    markers_to_check = [
        b'VERT', b'FACE', b'MESH', b'GEOM', b'POLY',
        b'vert', b'face', b'mesh', b'geom', b'poly'
    ]
    
    for marker in markers_to_check:
        pos = data.find(marker)
        if pos != -1:
            print(f"   Found '{marker.decode()}' at offset 0x{pos:x}")

def main():
    print("🔬 ADVANCED KSM FORMAT ANALYZER")
    print("=" * 60)
    
    models_dir = "models_for_blender"
    
    if not os.path.exists(models_dir):
        print(f"❌ Models directory not found: {models_dir}")
        return
    
    # Get .X files sorted by size
    x_files = []
    for file in os.listdir(models_dir):
        if file.lower().endswith('.x'):
            file_path = os.path.join(models_dir, file)
            size = os.path.getsize(file_path)
            x_files.append((file, size))
    
    x_files.sort(key=lambda x: x[1])
    
    # Analyze a few different sized files
    test_files = [
        x_files[4],      # Small weapon
        x_files[10],     # Medium model
        x_files[-5]      # Large character model
    ]
    
    for file_name, size in test_files:
        file_path = os.path.join(models_dir, file_name)
        analyze_ksm_detailed(file_path)
    
    print(f"\n💡 ANALYSIS COMPLETE!")
    print(f"Use the findings to create a more accurate converter.")

if __name__ == "__main__":
    main()
