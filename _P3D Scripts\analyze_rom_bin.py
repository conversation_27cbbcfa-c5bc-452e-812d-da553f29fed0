#!/usr/bin/env python3
"""
ROM .bin file analyzer - examines the structure of video game ROM binary files
"""

import os
import struct
import binascii

def analyze_bin_file(bin_path: str):
    """Analyze the structure and content of a ROM .bin file"""
    
    if not os.path.exists(bin_path):
        print(f"❌ File not found: {bin_path}")
        return
    
    with open(bin_path, "rb") as f:
        data = f.read()
    
    print(f"🔍 ANALYZING: {bin_path}")
    print("=" * 60)
    print(f"File size: {len(data):,} bytes ({len(data)/1024:.1f} KB)")
    
    # Check for common file signatures/headers
    print(f"\n📋 FILE HEADER ANALYSIS:")
    header = data[:64]
    print(f"First 64 bytes (hex): {header.hex()}")
    print(f"First 64 bytes (ASCII): {repr(header.decode('ascii', errors='ignore'))}")
    
    # Look for common game file signatures
    signatures = {
        b'P3D ': 'P3D Model File',
        b'RIFF': 'RIFF Container (WAV, AVI, etc.)',
        b'PNG\r\n\x1a\n': 'PNG Image',
        b'GIF8': 'GIF Image',
        b'JFIF': 'JPEG Image',
        b'BM': 'Bitmap Image',
        b'OggS': 'Ogg Audio',
        b'ID3': 'MP3 Audio',
        b'fLaC': 'FLAC Audio',
        b'MThd': 'MIDI File',
        b'FORM': 'IFF Container',
        b'PK\x03\x04': 'ZIP Archive',
        b'Rar!': 'RAR Archive',
        b'7z\xbc\xaf\x27\x1c': '7-Zip Archive',
        b'\x1f\x8b': 'GZIP Compressed',
        b'BZh': 'BZIP2 Compressed',
        b'\x89PNG': 'PNG Image',
        b'\xff\xd8\xff': 'JPEG Image',
        b'MDLX': '3D Model File',
        b'MDL': '3D Model File',
        b'OBJ': 'Wavefront OBJ',
        b'PLY': 'PLY 3D Model',
        b'STL': 'STL 3D Model',
    }
    
    found_signature = None
    for sig, desc in signatures.items():
        if data.startswith(sig):
            found_signature = desc
            print(f"✅ Detected format: {desc}")
            break
    
    if not found_signature:
        print("❓ Unknown format - analyzing structure...")
    
    # Analyze data patterns
    print(f"\n📊 DATA PATTERN ANALYSIS:")
    
    # Check for repeating patterns that might indicate structured data
    null_bytes = data.count(b'\x00')
    print(f"Null bytes: {null_bytes:,} ({null_bytes/len(data)*100:.1f}%)")
    
    # Look for text strings
    text_chars = sum(1 for b in data if 32 <= b <= 126)
    print(f"Printable ASCII: {text_chars:,} ({text_chars/len(data)*100:.1f}%)")
    
    # Check entropy (randomness)
    byte_counts = [0] * 256
    for byte in data:
        byte_counts[byte] += 1
    
    import math
    entropy = 0
    for count in byte_counts:
        if count > 0:
            p = count / len(data)
            entropy -= p * math.log2(p) if p > 0 else 0
    
    print(f"Data entropy: {entropy:.2f} (0=uniform, 8=random)")
    
    # Look for potential file boundaries/sections
    print(f"\n🔍 SEARCHING FOR EMBEDDED FILES:")
    
    # Common file headers to search for within the data
    embedded_signatures = [
        (b'P3D ', 'P3D Model'),
        (b'\x89PNG', 'PNG Image'),
        (b'\xff\xd8\xff', 'JPEG Image'),
        (b'GIF8', 'GIF Image'),
        (b'BM', 'Bitmap'),
        (b'RIFF', 'RIFF File'),
        (b'OggS', 'Ogg Audio'),
        (b'ID3', 'MP3 Audio'),
        (b'fLaC', 'FLAC Audio'),
        (b'MThd', 'MIDI'),
        (b'PK\x03\x04', 'ZIP Archive'),
    ]
    
    embedded_files = []
    for sig, desc in embedded_signatures:
        pos = 0
        while True:
            pos = data.find(sig, pos)
            if pos == -1:
                break
            embedded_files.append((pos, sig, desc))
            pos += len(sig)
    
    if embedded_files:
        print("Found potential embedded files:")
        for pos, sig, desc in embedded_files[:10]:  # Show first 10
            print(f"  {desc} at offset {pos:,} (0x{pos:x})")
        if len(embedded_files) > 10:
            print(f"  ... and {len(embedded_files) - 10} more")
    else:
        print("No obvious embedded files found")
    
    # Look for potential directory/index structures
    print(f"\n📁 LOOKING FOR DIRECTORY STRUCTURES:")
    
    # Check for patterns that might indicate file tables
    # Look for sequences of 32-bit integers (potential offsets/sizes)
    potential_offsets = []
    for i in range(0, min(1024, len(data) - 4), 4):
        value = struct.unpack('<I', data[i:i+4])[0]
        if 0 < value < len(data) and value % 4 == 0:  # Reasonable offset, aligned
            potential_offsets.append((i, value))
    
    if potential_offsets:
        print(f"Found {len(potential_offsets)} potential file offsets in first 1KB")
        for i, (pos, offset) in enumerate(potential_offsets[:5]):
            print(f"  Offset table entry at {pos}: points to {offset:,} (0x{offset:x})")
        if len(potential_offsets) > 5:
            print(f"  ... and {len(potential_offsets) - 5} more")
    
    # Show hex dump of different sections
    print(f"\n🔢 HEX DUMP SAMPLES:")
    sections = [
        ("Start", 0, 128),
        ("Middle", len(data)//2, 128),
        ("End", max(0, len(data) - 128), 128)
    ]
    
    for name, start, length in sections:
        end = min(start + length, len(data))
        section_data = data[start:end]
        print(f"\n{name} of file (offset {start:,}):")
        
        # Print hex dump in 16-byte rows
        for i in range(0, len(section_data), 16):
            row = section_data[i:i+16]
            hex_part = ' '.join(f'{b:02x}' for b in row)
            ascii_part = ''.join(chr(b) if 32 <= b <= 126 else '.' for b in row)
            print(f"{start + i:08x}: {hex_part:<48} {ascii_part}")
            if i >= 64:  # Limit output
                print("  ...")
                break

def extract_embedded_files(bin_path: str, output_dir: str):
    """Extract any embedded files found in the ROM .bin"""
    
    if not os.path.exists(bin_path):
        print(f"❌ File not found: {bin_path}")
        return
    
    os.makedirs(output_dir, exist_ok=True)
    
    with open(bin_path, "rb") as f:
        data = f.read()
    
    print(f"🔍 EXTRACTING FROM: {bin_path}")
    print(f"📁 OUTPUT DIRECTORY: {output_dir}")
    print("=" * 60)
    
    # Define extraction patterns
    patterns = [
        (b'P3D ', '.p3d', 'P3D Model'),
        (b'\x89PNG\r\n\x1a\n', '.png', 'PNG Image'),
        (b'\xff\xd8\xff', '.jpg', 'JPEG Image'),
        (b'GIF87a', '.gif', 'GIF Image'),
        (b'GIF89a', '.gif', 'GIF Image'),
        (b'BM', '.bmp', 'Bitmap Image'),
        (b'RIFF', '.wav', 'RIFF/WAV File'),
        (b'OggS', '.ogg', 'Ogg Audio'),
        (b'ID3', '.mp3', 'MP3 Audio'),
        (b'fLaC', '.flac', 'FLAC Audio'),
        (b'MThd', '.mid', 'MIDI File'),
    ]
    
    extracted_count = 0
    
    for signature, extension, description in patterns:
        pos = 0
        file_index = 0
        
        while True:
            pos = data.find(signature, pos)
            if pos == -1:
                break
            
            # Try to determine file size
            file_data = None
            
            if signature == b'P3D ':
                # P3D files have a specific structure
                try:
                    # Read potential file size from header
                    if pos + 12 <= len(data):
                        size = struct.unpack('<I', data[pos+8:pos+12])[0]
                        if size > 0 and pos + size <= len(data):
                            file_data = data[pos:pos + size]
                except:
                    pass
            
            elif signature.startswith(b'\xff\xd8\xff'):
                # JPEG files end with FF D9
                end_pos = data.find(b'\xff\xd9', pos + 2)
                if end_pos != -1:
                    file_data = data[pos:end_pos + 2]
            
            elif signature.startswith(b'\x89PNG'):
                # PNG files have IEND chunk at the end
                end_pos = data.find(b'IEND', pos + 8)
                if end_pos != -1:
                    file_data = data[pos:end_pos + 8]  # Include IEND + CRC
            
            elif signature == b'RIFF':
                # RIFF files have size in header
                try:
                    if pos + 8 <= len(data):
                        size = struct.unpack('<I', data[pos+4:pos+8])[0]
                        if size > 0 and pos + 8 + size <= len(data):
                            file_data = data[pos:pos + 8 + size]
                except:
                    pass
            
            # If we couldn't determine size, extract a reasonable chunk
            if file_data is None:
                # Extract up to next signature or reasonable size
                next_sig_pos = len(data)
                for other_sig, _, _ in patterns:
                    if other_sig != signature:
                        temp_pos = data.find(other_sig, pos + len(signature))
                        if temp_pos != -1 and temp_pos < next_sig_pos:
                            next_sig_pos = temp_pos
                
                max_size = min(1024 * 1024, next_sig_pos - pos)  # Max 1MB
                file_data = data[pos:pos + max_size]
            
            if file_data and len(file_data) > len(signature):
                filename = f"extracted_{file_index:03d}{extension}"
                filepath = os.path.join(output_dir, filename)
                
                with open(filepath, 'wb') as f:
                    f.write(file_data)
                
                print(f"✅ Extracted {description}: {filename} ({len(file_data):,} bytes)")
                extracted_count += 1
                file_index += 1
            
            pos += len(signature)
    
    print(f"\n🎉 Extraction complete! Found {extracted_count} files in {output_dir}")

if __name__ == "__main__":
    bin_file = "D_MODEL.BIN"
    output_folder = "extracted_files"
    
    print("🎮 ROM .BIN File Analyzer & Extractor")
    print("=" * 60)
    
    # First analyze the file
    analyze_bin_file(bin_file)
    
    # Then extract any embedded files
    print("\n" + "=" * 60)
    extract_embedded_files(bin_file, output_folder)
