#!/usr/bin/env python3
"""
Deep KSM Format Analysis
Analyze the binary structure to understand the data layout
"""

import os
import struct

def analyze_ksm_structure(file_path):
    """Deep analysis of KSM file structure"""
    
    print(f"\n🔬 DEEP ANALYSIS: {os.path.basename(file_path)}")
    print("=" * 60)
    
    try:
        with open(file_path, 'rb') as f:
            data = f.read()
    except Exception as e:
        print(f"❌ Error reading file: {e}")
        return
    
    file_size = len(data)
    print(f"📊 File size: {file_size:,} bytes")
    
    # Parse header
    if len(data) < 32:
        print("❌ File too small")
        return
    
    signature = data[:4]
    version = struct.unpack('<I', data[4:8])[0]
    data_size = struct.unpack('<I', data[8:12])[0]
    
    print(f"📋 Header:")
    print(f"   Signature: {signature}")
    print(f"   Version: {version} (0x{version:x})")
    print(f"   Data size: {data_size:,} bytes")
    
    # Analyze data sections
    print(f"\n📊 Data Structure Analysis:")
    
    # Look for repeating patterns that might indicate data arrays
    offset = 12  # Start after basic header
    
    # Check for potential data blocks
    while offset < len(data) - 16:
        # Try to read potential block header
        if offset + 8 <= len(data):
            block_size = struct.unpack('<I', data[offset:offset+4])[0]
            block_type = struct.unpack('<I', data[offset+4:offset+8])[0]
            
            # Check if this looks like a reasonable block
            if (block_size > 0 and block_size < file_size and 
                offset + block_size <= file_size):
                
                print(f"\n🔍 Potential block at offset 0x{offset:x}:")
                print(f"   Size: {block_size:,} bytes")
                print(f"   Type: 0x{block_type:x} ({block_type})")
                
                # Analyze block content
                block_data = data[offset+8:offset+8+min(block_size, 64)]
                
                # Check if it contains floats (vertices)
                float_count = 0
                for i in range(0, len(block_data)-4, 4):
                    try:
                        val = struct.unpack('<f', block_data[i:i+4])[0]
                        if abs(val) < 1000 and not (val == 0):
                            float_count += 1
                    except:
                        pass
                
                if float_count > 3:
                    print(f"   🎮 Contains {float_count} potential float values (vertices?)")
                    
                    # Show first few floats
                    print(f"   First floats:")
                    for i in range(0, min(48, len(block_data)), 12):
                        if i + 12 <= len(block_data):
                            x, y, z = struct.unpack('<3f', block_data[i:i+12])
                            print(f"     {x:.3f}, {y:.3f}, {z:.3f}")
                
                # Check if it contains integers (indices)
                int_count = 0
                max_int = 0
                for i in range(0, len(block_data)-4, 4):
                    try:
                        val = struct.unpack('<I', block_data[i:i+4])[0]
                        if val < 100000:  # Reasonable index range
                            int_count += 1
                            max_int = max(max_int, val)
                    except:
                        pass
                
                if int_count > 3:
                    print(f"   🔢 Contains {int_count} potential indices (max: {max_int})")
                
                offset += 8 + block_size
            else:
                offset += 4
        else:
            break
    
    # Look for specific patterns
    print(f"\n🔍 Pattern Analysis:")
    
    # Count different data types
    float_positions = []
    int_positions = []
    
    for offset in range(12, len(data) - 4, 4):
        try:
            # Try as float
            val_f = struct.unpack('<f', data[offset:offset+4])[0]
            if abs(val_f) < 1000 and abs(val_f) > 0.001:
                float_positions.append(offset)
            
            # Try as int
            val_i = struct.unpack('<I', data[offset:offset+4])[0]
            if val_i < 100000:
                int_positions.append(offset)
                
        except:
            continue
    
    print(f"   Float-like values: {len(float_positions)}")
    print(f"   Int-like values: {len(int_positions)}")
    
    # Look for vertex-like patterns (groups of 3 floats)
    vertex_groups = []
    for i in range(len(float_positions) - 2):
        pos1 = float_positions[i]
        pos2 = float_positions[i + 1]
        pos3 = float_positions[i + 2]
        
        # Check if they're consecutive (12 bytes apart for xyz)
        if pos2 - pos1 == 4 and pos3 - pos2 == 4:
            vertex_groups.append(pos1)
    
    print(f"   Potential vertex groups: {len(vertex_groups)}")
    
    if vertex_groups:
        print(f"   First vertex group at offset: 0x{vertex_groups[0]:x}")
        
        # Show first few vertices
        for i, offset in enumerate(vertex_groups[:5]):
            if offset + 12 <= len(data):
                x, y, z = struct.unpack('<3f', data[offset:offset+12])
                print(f"     Vertex {i}: {x:.3f}, {y:.3f}, {z:.3f}")

def main():
    print("🔬 KSM DEEP STRUCTURE ANALYZER")
    print("=" * 60)
    
    models_dir = "models_for_blender"
    
    if not os.path.exists(models_dir):
        print(f"❌ Models directory not found: {models_dir}")
        return
    
    # Get .X files sorted by size
    x_files = []
    for file in os.listdir(models_dir):
        if file.lower().endswith('.x'):
            file_path = os.path.join(models_dir, file)
            size = os.path.getsize(file_path)
            x_files.append((file, size))
    
    x_files.sort(key=lambda x: x[1])  # Sort by size
    
    # Analyze a few different files
    test_files = [
        x_files[0],      # Smallest
        x_files[len(x_files)//4],  # Small-medium
        x_files[len(x_files)//2],  # Medium
        x_files[-1]      # Largest
    ]
    
    for file_name, size in test_files:
        file_path = os.path.join(models_dir, file_name)
        analyze_ksm_structure(file_path)
    
    print(f"\n💡 ANALYSIS COMPLETE!")
    print(f"Look for patterns in the block structures to understand the format.")

if __name__ == "__main__":
    main()
