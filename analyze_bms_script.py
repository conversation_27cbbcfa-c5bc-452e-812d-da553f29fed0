#!/usr/bin/env python3
"""
BMS Script Analyzer - Analyze .bms scripts to understand extraction format
"""

import os
import re

def analyze_bms_script():
    """Find and analyze BMS scripts in current directory"""
    
    print("🔍 BMS SCRIPT ANALYZER")
    print("=" * 50)
    
    bms_files = [f for f in os.listdir('.') if f.lower().endswith('.bms')]
    
    if not bms_files:
        print("❌ No .bms files found in current directory")
        return
    
    for bms_file in bms_files:
        print(f"\n📄 Analyzing: {bms_file}")
        print("-" * 30)
        
        try:
            with open(bms_file, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
            
            # Basic script info
            lines = content.split('\n')
            print(f"📊 Script info:")
            print(f"   Lines: {len(lines)}")
            print(f"   Size: {len(content)} bytes")
            
            # Look for key BMS commands
            commands = {
                'get': 0,
                'set': 0,
                'math': 0,
                'string': 0,
                'for': 0,
                'next': 0,
                'if': 0,
                'endif': 0,
                'log': 0,
                'clog': 0,
                'encryption': 0,
                'compression': 0
            }
            
            file_patterns = []
            encryption_info = []
            compression_info = []
            
            for line in lines:
                line_clean = line.strip().lower()
                
                if not line_clean or line_clean.startswith('#'):
                    continue
                
                # Count commands
                for cmd in commands:
                    if line_clean.startswith(cmd + ' '):
                        commands[cmd] += 1
                
                # Look for file patterns
                if 'log' in line_clean and any(ext in line_clean for ext in ['.', 'name', 'filename']):
                    file_patterns.append(line.strip())
                
                # Look for encryption/compression
                if any(word in line_clean for word in ['encrypt', 'decrypt', 'xor', 'key']):
                    encryption_info.append(line.strip())
                
                if any(word in line_clean for word in ['compress', 'decompress', 'zlib', 'lzma']):
                    compression_info.append(line.strip())
            
            # Report findings
            print(f"\n🔧 BMS Commands used:")
            for cmd, count in commands.items():
                if count > 0:
                    print(f"   {cmd.upper()}: {count} times")
            
            if file_patterns:
                print(f"\n📁 File extraction patterns:")
                for pattern in file_patterns[:5]:  # Show first 5
                    print(f"   {pattern}")
                if len(file_patterns) > 5:
                    print(f"   ... and {len(file_patterns) - 5} more")
            
            if encryption_info:
                print(f"\n🔐 Encryption/Decryption:")
                for info in encryption_info[:3]:
                    print(f"   {info}")
            
            if compression_info:
                print(f"\n📦 Compression:")
                for info in compression_info[:3]:
                    print(f"   {info}")
            
            # Try to determine what game/format this is for
            content_lower = content.lower()
            game_hints = []
            
            if 'kom' in content_lower:
                game_hints.append("KOM archive format")
            if 'elsword' in content_lower:
                game_hints.append("Elsword game")
            if 'mmo' in content_lower:
                game_hints.append("MMO game")
            if 'model' in content_lower:
                game_hints.append("3D model extraction")
            if 'texture' in content_lower:
                game_hints.append("Texture extraction")
            
            if game_hints:
                print(f"\n🎮 Detected format/game hints:")
                for hint in game_hints:
                    print(f"   • {hint}")
            
            # Show first few lines of script
            print(f"\n📝 Script preview (first 10 lines):")
            for i, line in enumerate(lines[:10]):
                if line.strip():
                    print(f"   {i+1:2d}: {line}")
            
        except Exception as e:
            print(f"❌ Error reading {bms_file}: {e}")

def create_test_bms_script():
    """Create a basic test BMS script for KOM files"""
    
    test_script = """# Basic KOM extraction script
# This is a generic script - replace with your specific one

# Get file count
get FILES long
print "Files to extract: %FILES%"

# Extract files
for i = 0 < FILES
    # Get filename
    get NAMESIZE long
    getdstring NAME NAMESIZE
    
    # Get file info
    get OFFSET long
    get SIZE long
    
    # Extract file
    log NAME OFFSET SIZE
    
next i
"""
    
    with open("test_kom.bms", 'w') as f:
        f.write(test_script)
    
    print("📝 Created test_kom.bms - basic KOM extraction script")
    print("   Replace this with your actual BMS script for better results")

def main():
    analyze_bms_script()
    
    # If no BMS files found, offer to create a test one
    bms_files = [f for f in os.listdir('.') if f.lower().endswith('.bms')]
    if not bms_files:
        print(f"\n💡 Would you like me to create a basic test BMS script?")
        create_test_bms_script()

if __name__ == "__main__":
    main()
