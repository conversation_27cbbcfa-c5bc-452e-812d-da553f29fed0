#!/usr/bin/env python3
"""
Decompress extracted KOM files - Handle algo2/algo3 compressed files
"""

import os
import struct
import zlib
import lzma

def try_decompress_algo2(file_path, output_path):
    """Try to decompress algo2 compressed files (likely TGA textures)"""
    
    print(f"🔄 Decompressing algo2: {os.path.basename(file_path)}")
    
    try:
        with open(file_path, 'rb') as f:
            data = f.read()
        
        # Try different decompression methods
        methods = [
            ("zlib", lambda d: zlib.decompress(d)),
            ("zlib_skip_header", lambda d: zlib.decompress(d[4:])),
            ("zlib_skip_8", lambda d: zlib.decompress(d[8:])),
            ("lzma", lambda d: lzma.decompress(d)),
            ("raw_copy", lambda d: d)
        ]
        
        for method_name, decompress_func in methods:
            try:
                decompressed = decompress_func(data)
                
                # Check if result looks like TGA file
                if len(decompressed) > 18:  # TGA header is 18 bytes
                    # TGA files often start with specific patterns
                    if (decompressed[2] == 2 or decompressed[2] == 10):  # TGA image type
                        print(f"   ✅ Success with {method_name}! Size: {len(decompressed):,} bytes")
                        
                        with open(output_path, 'wb') as f:
                            f.write(decompressed)
                        return True
                
            except Exception as e:
                continue
        
        print(f"   ❌ All decompression methods failed")
        return False
        
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return False

def try_decompress_algo3(file_path, output_path):
    """Try to decompress algo3 compressed files (likely Lua scripts)"""
    
    print(f"🔄 Decompressing algo3: {os.path.basename(file_path)}")
    
    try:
        with open(file_path, 'rb') as f:
            data = f.read()
        
        # Try different decompression methods
        methods = [
            ("zlib", lambda d: zlib.decompress(d)),
            ("zlib_skip_header", lambda d: zlib.decompress(d[4:])),
            ("zlib_skip_8", lambda d: zlib.decompress(d[8:])),
            ("lzma", lambda d: lzma.decompress(d)),
            ("raw_copy", lambda d: d)
        ]
        
        for method_name, decompress_func in methods:
            try:
                decompressed = decompress_func(data)
                
                # Check if result looks like text/Lua
                try:
                    text = decompressed.decode('utf-8', errors='ignore')
                    if any(keyword in text.lower() for keyword in ['function', 'local', 'end', 'if', 'then']):
                        print(f"   ✅ Success with {method_name}! Lua script detected")
                        
                        with open(output_path, 'wb') as f:
                            f.write(decompressed)
                        return True
                except:
                    pass
                
            except Exception as e:
                continue
        
        print(f"   ❌ All decompression methods failed")
        return False
        
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return False

def analyze_file_headers(extracted_dir):
    """Analyze file headers to understand compression format"""
    
    print(f"\n🔍 ANALYZING FILE HEADERS")
    print("=" * 50)
    
    for root, dirs, files in os.walk(extracted_dir):
        for file in files:
            file_path = os.path.join(root, file)
            
            try:
                with open(file_path, 'rb') as f:
                    header = f.read(32)  # Read first 32 bytes
                
                print(f"\n📄 {file}:")
                print(f"   Size: {os.path.getsize(file_path):,} bytes")
                print(f"   Header (hex): {header[:16].hex()}")
                print(f"   Header (ascii): {header[:16].decode('ascii', errors='replace')}")
                
                # Check for compression signatures
                if header.startswith(b'\x78\x9c') or header.startswith(b'\x78\xda'):
                    print(f"   🔍 Detected: zlib compression")
                elif header.startswith(b'\xfd7zXZ'):
                    print(f"   🔍 Detected: LZMA compression")
                elif header[4:8] == b'\x78\x9c' or header[4:8] == b'\x78\xda':
                    print(f"   🔍 Detected: zlib with 4-byte header")
                elif header[8:12] == b'\x78\x9c' or header[8:12] == b'\x78\xda':
                    print(f"   🔍 Detected: zlib with 8-byte header")
                
            except Exception as e:
                print(f"   ❌ Error reading {file}: {e}")

def decompress_all_files():
    """Decompress all extracted files"""
    
    extracted_dir = "extracted"
    decompressed_dir = "decompressed"
    
    if not os.path.exists(extracted_dir):
        print(f"❌ Extracted directory not found: {extracted_dir}")
        return
    
    os.makedirs(decompressed_dir, exist_ok=True)
    
    print(f"\n🔄 DECOMPRESSING FILES")
    print("=" * 50)
    
    success_count = 0
    total_count = 0
    
    for root, dirs, files in os.walk(extracted_dir):
        for file in files:
            file_path = os.path.join(root, file)
            total_count += 1
            
            # Determine output filename
            if '.algo2.' in file:
                # Remove algo2 compression indicator
                base_name = file.replace('.algo2.1048620', '')
                output_path = os.path.join(decompressed_dir, base_name)
                
                if try_decompress_algo2(file_path, output_path):
                    success_count += 1
                    
            elif '.algo3.' in file:
                # Remove algo3 compression indicator
                base_name = file.replace('.algo3.', '.')
                # Remove the number at the end
                if '.' in base_name:
                    parts = base_name.split('.')
                    if parts[-1].isdigit():
                        base_name = '.'.join(parts[:-1])
                
                output_path = os.path.join(decompressed_dir, base_name)
                
                if try_decompress_algo3(file_path, output_path):
                    success_count += 1
            else:
                # Copy non-compressed files
                output_path = os.path.join(decompressed_dir, file)
                try:
                    import shutil
                    shutil.copy2(file_path, output_path)
                    print(f"📄 Copied: {file}")
                    success_count += 1
                except Exception as e:
                    print(f"❌ Copy failed for {file}: {e}")
    
    print(f"\n🎉 DECOMPRESSION COMPLETE!")
    print(f"✅ Successfully processed: {success_count}/{total_count} files")
    
    # List decompressed files
    if os.path.exists(decompressed_dir):
        print(f"\n📁 Decompressed files:")
        for file in os.listdir(decompressed_dir):
            file_path = os.path.join(decompressed_dir, file)
            size = os.path.getsize(file_path)
            print(f"   📄 {file} ({size:,} bytes)")

def main():
    print("🗜️  KOM FILE DECOMPRESSOR")
    print("=" * 60)
    
    # First analyze headers to understand compression
    analyze_file_headers("extracted")
    
    # Then decompress files
    decompress_all_files()
    
    print(f"\n🚀 Next steps:")
    print(f"   1. Check 'decompressed/' folder for readable files")
    print(f"   2. Look for .tga texture files")
    print(f"   3. Check .lua files for game logic")
    print(f"   4. If this KOM contains UI files, try other KOM files for 3D models")

if __name__ == "__main__":
    main()
