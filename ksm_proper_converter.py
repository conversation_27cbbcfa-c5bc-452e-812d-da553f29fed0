#!/usr/bin/env python3
"""
Proper KSM Converter
Based on detailed analysis of the KSM format structure
"""

import os
import struct
import math

class KSMProperParser:
    def __init__(self, file_path):
        self.file_path = file_path
        self.data = None
        self.vertices = []
        self.faces = []
        self.vertex_regions = []
        self.face_regions = []
        
    def load_file(self):
        """Load the KSM file"""
        try:
            with open(self.file_path, 'rb') as f:
                self.data = f.read()
            return True
        except Exception as e:
            print(f"❌ Error loading file: {e}")
            return False
    
    def find_vertex_regions(self):
        """Find regions containing vertex data"""
        print(f"🎮 Finding vertex regions...")
        
        current_region = None
        
        for offset in range(12, len(self.data) - 12, 4):
            try:
                val = struct.unpack('<f', self.data[offset:offset+4])[0]
                
                # Check if this looks like a reasonable coordinate
                if (abs(val) < 1000 and abs(val) > 0.001 and 
                    not math.isnan(val) and not math.isinf(val)):
                    
                    if current_region is None:
                        current_region = {'start': offset, 'count': 1, 'values': [val]}
                    elif offset - current_region['start'] == current_region['count'] * 4:
                        current_region['count'] += 1
                        current_region['values'].append(val)
                    else:
                        # End current region if it has enough data
                        if current_region['count'] >= 9:  # At least 3 vertices
                            self.vertex_regions.append(current_region)
                        current_region = {'start': offset, 'count': 1, 'values': [val]}
                else:
                    # End current region
                    if current_region and current_region['count'] >= 9:
                        self.vertex_regions.append(current_region)
                    current_region = None
                    
            except struct.error:
                continue
        
        # Add final region
        if current_region and current_region['count'] >= 9:
            self.vertex_regions.append(current_region)
        
        print(f"   Found {len(self.vertex_regions)} vertex regions")
        return len(self.vertex_regions) > 0
    
    def extract_vertices(self):
        """Extract vertices from the found regions"""
        print(f"📊 Extracting vertices...")
        
        total_vertices = 0
        
        for i, region in enumerate(self.vertex_regions):
            values = region['values']
            region_vertices = []
            
            # Extract vertices (groups of 3 floats)
            for j in range(0, len(values) - 2, 3):
                x, y, z = values[j], values[j+1], values[j+2]
                
                # Additional validation
                if (abs(x) < 500 and abs(y) < 500 and abs(z) < 500 and
                    not (x == 0 and y == 0 and z == 0)):
                    region_vertices.append((x, y, z))
            
            if region_vertices:
                print(f"   Region {i}: {len(region_vertices)} vertices")
                self.vertices.extend(region_vertices)
                total_vertices += len(region_vertices)
                
                # Show first few vertices
                for k, (x, y, z) in enumerate(region_vertices[:3]):
                    print(f"     Vertex {k}: {x:.3f}, {y:.3f}, {z:.3f}")
        
        print(f"   Total vertices: {total_vertices}")
        return total_vertices > 0
    
    def find_face_indices(self):
        """Find face indices based on vertex count"""
        if not self.vertices:
            return False
            
        print(f"🔺 Finding face indices...")
        
        max_vertex_index = len(self.vertices) - 1
        face_count = 0
        
        # Look for groups of 3 integers that could be face indices
        for offset in range(12, len(self.data) - 12, 4):
            try:
                # Read 3 consecutive integers
                i1 = struct.unpack('<I', self.data[offset:offset+4])[0]
                i2 = struct.unpack('<I', self.data[offset+4:offset+8])[0]
                i3 = struct.unpack('<I', self.data[offset+8:offset+12])[0]
                
                # Check if these could be valid vertex indices
                if (0 <= i1 <= max_vertex_index and 
                    0 <= i2 <= max_vertex_index and 
                    0 <= i3 <= max_vertex_index and
                    i1 != i2 and i2 != i3 and i1 != i3):
                    
                    self.faces.append((i1, i2, i3))
                    face_count += 1
                    
                    # Show first few faces
                    if face_count <= 5:
                        print(f"     Face {face_count-1}: {i1}, {i2}, {i3}")
                        
            except struct.error:
                continue
        
        print(f"   Found {face_count} faces")
        return face_count > 0
    
    def generate_fallback_faces(self):
        """Generate faces if none were found"""
        if self.faces or len(self.vertices) < 3:
            return
            
        print(f"🔧 Generating fallback faces...")
        
        # Simple triangulation - create triangles from consecutive vertices
        face_count = 0
        for i in range(0, len(self.vertices) - 2, 3):
            if i + 2 < len(self.vertices):
                self.faces.append((i, i + 1, i + 2))
                face_count += 1
        
        print(f"   Generated {face_count} fallback faces")
    
    def write_obj(self, output_path):
        """Write to OBJ file"""
        try:
            with open(output_path, 'w') as f:
                f.write("# Converted from KSM format\n")
                f.write(f"# Source: {os.path.basename(self.file_path)}\n")
                f.write(f"# Vertex regions: {len(self.vertex_regions)}\n\n")
                
                # Write vertices
                for x, y, z in self.vertices:
                    f.write(f"v {x:.6f} {y:.6f} {z:.6f}\n")
                
                f.write("\n")
                
                # Write faces (OBJ uses 1-based indexing)
                for i1, i2, i3 in self.faces:
                    f.write(f"f {i1+1} {i2+1} {i3+1}\n")
            
            return True
            
        except Exception as e:
            print(f"❌ Error writing OBJ: {e}")
            return False

def convert_ksm_proper(ksm_path, obj_path):
    """Convert KSM file using proper analysis"""
    print(f"\n🔄 Converting: {os.path.basename(ksm_path)}")
    print("-" * 50)
    
    parser = KSMProperParser(ksm_path)
    
    # Load file
    if not parser.load_file():
        return False
    
    # Find vertex regions
    if not parser.find_vertex_regions():
        print("❌ Could not find vertex regions")
        return False
    
    # Extract vertices
    if not parser.extract_vertices():
        print("❌ Could not extract vertices")
        return False
    
    # Find face indices
    if not parser.find_face_indices():
        print("⚠️  Could not find face indices, generating fallback faces")
        parser.generate_fallback_faces()
    
    # Write OBJ
    if parser.write_obj(obj_path):
        file_size = os.path.getsize(obj_path)
        print(f"✅ Success! Created {os.path.basename(obj_path)} ({file_size:,} bytes)")
        print(f"   Vertices: {len(parser.vertices)}")
        print(f"   Faces: {len(parser.faces)}")
        print(f"   Vertex regions: {len(parser.vertex_regions)}")
        return True
    
    return False

def main():
    print("🔧 PROPER KSM CONVERTER")
    print("=" * 50)
    
    models_dir = "models_for_blender"
    output_dir = "proper_obj_models"
    
    if not os.path.exists(models_dir):
        print(f"❌ Models directory not found: {models_dir}")
        return
    
    # Create output directory
    if os.path.exists(output_dir):
        import shutil
        shutil.rmtree(output_dir)
    os.makedirs(output_dir, exist_ok=True)
    
    # Get .X files
    x_files = [f for f in os.listdir(models_dir) if f.lower().endswith('.x')]
    
    if not x_files:
        print(f"❌ No .X files found in {models_dir}")
        return
    
    print(f"📁 Found {len(x_files)} KSM files to convert")
    
    # Sort by size and try a few different sizes
    x_file_sizes = []
    for x_file in x_files:
        x_path = os.path.join(models_dir, x_file)
        size = os.path.getsize(x_path)
        x_file_sizes.append((x_file, size))
    
    x_file_sizes.sort(key=lambda x: x[1])
    
    # Test files: small, medium, large
    test_files = [
        x_file_sizes[5],      # Small model
        x_file_sizes[len(x_file_sizes)//2],  # Medium model
        x_file_sizes[-3]      # Large model
    ]
    
    successful_conversions = 0
    failed_conversions = 0
    
    for i, (x_file, size) in enumerate(test_files):
        x_path = os.path.join(models_dir, x_file)
        obj_file = os.path.splitext(x_file)[0] + '.obj'
        obj_path = os.path.join(output_dir, obj_file)
        
        print(f"\n📦 [{i+1}/3] {x_file} ({size:,} bytes)")
        
        if convert_ksm_proper(x_path, obj_path):
            successful_conversions += 1
        else:
            failed_conversions += 1
    
    # Summary
    print(f"\n🎉 CONVERSION COMPLETE!")
    print("=" * 50)
    print(f"✅ Successful: {successful_conversions}")
    print(f"❌ Failed: {failed_conversions}")
    
    if successful_conversions > 0:
        print(f"\n📁 Converted files are in: {output_dir}/")
        print(f"🚀 Import these OBJ files into Blender:")
        print(f"   File → Import → Wavefront (.obj)")
        
        # List converted files
        obj_files = [f for f in os.listdir(output_dir) if f.lower().endswith('.obj')]
        for obj_file in sorted(obj_files):
            obj_path = os.path.join(output_dir, obj_file)
            size = os.path.getsize(obj_path)
            print(f"   🎮 {obj_file} ({size:,} bytes)")

if __name__ == "__main__":
    main()
