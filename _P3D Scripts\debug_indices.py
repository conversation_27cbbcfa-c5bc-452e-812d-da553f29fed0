#!/usr/bin/env python3
"""
Debug script to check index buffer extraction
"""

from p3d_bin_to_obj import extract_json_from_bin, find_buffer_start
import struct

def debug_indices():
    with open('34871HefLI.bin', 'rb') as f:
        data = f.read()
    
    gltf = extract_json_from_bin(data)
    buffer_start = find_buffer_start(data)
    
    print(f"Buffer starts at: {buffer_start}")
    
    # Check face primitive (primitive 5)
    prim = gltf['meshes'][0]['primitives'][5]
    indices_accessor_idx = prim['indices']
    
    accessor = gltf['accessors'][indices_accessor_idx]
    buffer_view = gltf['bufferViews'][accessor['bufferView']]
    
    print(f"Accessor: {accessor}")
    print(f"Buffer view: {buffer_view}")
    
    # Calculate offset
    offset = buffer_start + buffer_view.get('byteOffset', 0) + accessor.get('byteOffset', 0)
    count = accessor['count']
    
    print(f"Calculated offset: {offset}")
    print(f"Count: {count}")
    print(f"Expected range: 0 to {accessor['max'][0]}")
    
    # Read raw data
    stride = 2  # UNSIGNED_SHORT = 2 bytes
    raw_data = data[offset:offset + stride * count]
    
    print(f"Raw data length: {len(raw_data)} bytes (expected: {stride * count})")
    
    # Unpack first 20 indices
    indices = list(struct.iter_unpack('H', raw_data))
    first_20 = [idx[0] for idx in indices[:20]]
    
    print(f"First 20 indices: {first_20}")
    print(f"Min index: {min(idx[0] for idx in indices)}")
    print(f"Max index: {max(idx[0] for idx in indices)}")

if __name__ == "__main__":
    debug_indices()
