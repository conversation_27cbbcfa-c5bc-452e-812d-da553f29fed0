#!/usr/bin/env python3
"""
Try All BMS Scripts - Test different BMS scripts on KOM files to find 3D models
"""

import os
import subprocess
import shutil

def find_all_bms_scripts():
    """Find all BMS scripts in current directory and subdirectories"""
    
    bms_scripts = []
    
    # Check current directory
    for file in os.listdir('.'):
        if file.lower().endswith('.bms'):
            bms_scripts.append(file)
    
    # Check CyberConv/bms scripts directory
    bms_dir = "CyberConv/bms scripts"
    if os.path.exists(bms_dir):
        for file in os.listdir(bms_dir):
            if file.lower().endswith('.bms'):
                bms_scripts.append(os.path.join(bms_dir, file))
    
    return bms_scripts

def extract_with_bms_script(kom_file, bms_script, quickbms_exe):
    """Extract KOM file using a specific BMS script"""
    
    script_name = os.path.basename(bms_script)
    output_dir = f"extracted_{script_name.replace('.bms', '')}"
    
    print(f"\n🔧 TRYING BMS SCRIPT: {script_name}")
    print("=" * 50)
    
    # Clean output directory
    if os.path.exists(output_dir):
        shutil.rmtree(output_dir)
    os.makedirs(output_dir, exist_ok=True)
    
    try:
        cmd = [quickbms_exe, bms_script, kom_file, output_dir]
        print(f"🔧 Running: {' '.join(cmd)}")
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=120)
        
        if result.returncode == 0:
            print("✅ Extraction successful!")
            
            # Count and analyze extracted files
            extracted_files = []
            model_files = []
            texture_files = []
            large_files = []
            
            for root, dirs, files in os.walk(output_dir):
                for file in files:
                    file_path = os.path.join(root, file)
                    file_size = os.path.getsize(file_path)
                    file_ext = os.path.splitext(file)[1].lower()
                    
                    extracted_files.append((file, file_size, file_ext))
                    
                    # Look for model files
                    model_extensions = ['.mesh', '.obj', '.fbx', '.dae', '.3ds', '.mdl', '.smd', '.x', '.md2', '.md3']
                    if file_ext in model_extensions:
                        model_files.append((file, file_size))
                    
                    # Look for texture files
                    texture_extensions = ['.png', '.jpg', '.jpeg', '.bmp', '.tga', '.dds']
                    if file_ext in texture_extensions:
                        texture_files.append((file, file_size))
                    
                    # Track large files
                    if file_size > 50000:  # >50KB
                        large_files.append((file, file_size, file_ext))
            
            print(f"📁 Extracted {len(extracted_files)} files")
            
            # Report findings
            if model_files:
                print(f"🎮 FOUND {len(model_files)} MODEL FILES:")
                for file, size in sorted(model_files, key=lambda x: x[1], reverse=True):
                    print(f"   📦 {file} ({size:,} bytes)")
                return True, len(model_files), output_dir
            
            if texture_files:
                print(f"🎨 Found {len(texture_files)} texture files")
            
            if large_files:
                print(f"📄 Large files (potential models):")
                for file, size, ext in sorted(large_files, key=lambda x: x[1], reverse=True)[:5]:
                    print(f"   📄 {file} ({size:,} bytes) {ext}")
            
            # Show file type summary
            file_types = {}
            for file, size, ext in extracted_files:
                if ext in file_types:
                    file_types[ext] += 1
                else:
                    file_types[ext] = 1
            
            print(f"📊 File types:")
            for ext, count in sorted(file_types.items(), key=lambda x: x[1], reverse=True)[:10]:
                print(f"   {ext if ext else '(no ext)'}: {count}")
            
            return False, 0, output_dir
        
        else:
            print(f"❌ Extraction failed:")
            if result.stderr:
                print(f"   Error: {result.stderr[:200]}")
            return False, 0, None
            
    except subprocess.TimeoutExpired:
        print("⏰ Extraction timed out")
        return False, 0, None
    except Exception as e:
        print(f"❌ Error: {e}")
        return False, 0, None

def main():
    print("🔍 BMS SCRIPT TESTER")
    print("=" * 60)
    
    # Find tools
    quickbms_exe = "QuickBMS/quickbms.exe"
    kom_file = "data179.kom"
    
    if not os.path.exists(quickbms_exe):
        print(f"❌ QuickBMS not found: {quickbms_exe}")
        return
    
    if not os.path.exists(kom_file):
        print(f"❌ KOM file not found: {kom_file}")
        return
    
    # Find all BMS scripts
    bms_scripts = find_all_bms_scripts()
    
    if not bms_scripts:
        print("❌ No BMS scripts found")
        return
    
    print(f"📄 Found {len(bms_scripts)} BMS scripts:")
    for script in bms_scripts:
        print(f"   📄 {script}")
    
    # Test each BMS script
    results = []
    
    for bms_script in bms_scripts:
        success, model_count, output_dir = extract_with_bms_script(kom_file, bms_script, quickbms_exe)
        
        results.append({
            'script': os.path.basename(bms_script),
            'success': success,
            'model_count': model_count,
            'output_dir': output_dir
        })
    
    # Summary
    print(f"\n🎉 TESTING COMPLETE!")
    print("=" * 60)
    
    successful_scripts = [r for r in results if r['success']]
    
    if successful_scripts:
        print(f"✅ Scripts that found 3D models:")
        for result in sorted(successful_scripts, key=lambda x: x['model_count'], reverse=True):
            print(f"   🎮 {result['script']}: {result['model_count']} models in {result['output_dir']}")
        
        print(f"\n🚀 Next steps:")
        print(f"   1. Check the output directories for .mesh files")
        print(f"   2. Use mesh_to_fbx_converter.py to convert models")
        print(f"   3. Import FBX files into Blender")
    else:
        print(f"❌ No BMS scripts found 3D models in this KOM file")
        print(f"\n💡 Suggestions:")
        print(f"   1. Try different KOM files (data001.kom, data002.kom, etc.)")
        print(f"   2. Look for KOM files with names like 'model', 'character', 'mesh'")
        print(f"   3. Some games store models in different archive formats")
        
        # Show what was found
        print(f"\n📊 What was extracted:")
        for result in results:
            if result['output_dir'] and os.path.exists(result['output_dir']):
                file_count = len([f for f in os.listdir(result['output_dir']) if os.path.isfile(os.path.join(result['output_dir'], f))])
                print(f"   📄 {result['script']}: {file_count} files")

if __name__ == "__main__":
    main()
