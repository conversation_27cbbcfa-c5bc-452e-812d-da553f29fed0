#!/usr/bin/env python3
"""
BMP to PNG Converter - Convert extracted BMP files to PNG format
"""

import os
import sys
from PIL import Image
import glob

def convert_bmp_to_png(input_dir: str, output_dir: str = None, keep_originals: bool = True):
    """Convert all BMP files in a directory to PNG format"""
    
    if not os.path.exists(input_dir):
        print(f"❌ Input directory not found: {input_dir}")
        return
    
    # Set output directory
    if output_dir is None:
        output_dir = os.path.join(input_dir, "png_converted")
    
    os.makedirs(output_dir, exist_ok=True)
    
    # Find all BMP files
    bmp_pattern = os.path.join(input_dir, "*.bmp")
    bmp_files = glob.glob(bmp_pattern)
    
    if not bmp_files:
        print(f"❌ No BMP files found in {input_dir}")
        return
    
    print(f"🔄 Converting {len(bmp_files)} BMP files to PNG...")
    print(f"📁 Input directory: {input_dir}")
    print(f"📁 Output directory: {output_dir}")
    print("=" * 60)
    
    converted_count = 0
    failed_count = 0
    total_size_before = 0
    total_size_after = 0
    
    for bmp_file in sorted(bmp_files):
        try:
            # Get file info
            filename = os.path.basename(bmp_file)
            name_without_ext = os.path.splitext(filename)[0]
            png_filename = f"{name_without_ext}.png"
            png_path = os.path.join(output_dir, png_filename)
            
            # Get original file size
            original_size = os.path.getsize(bmp_file)
            total_size_before += original_size
            
            # Open and convert the image
            with Image.open(bmp_file) as img:
                # Get image info
                width, height = img.size
                mode = img.mode
                
                # Convert to PNG with optimization
                img.save(png_path, "PNG", optimize=True)
                
                # Get new file size
                new_size = os.path.getsize(png_path)
                total_size_after += new_size
                
                # Calculate compression ratio
                compression_ratio = (1 - new_size / original_size) * 100 if original_size > 0 else 0
                
                print(f"✅ {filename} → {png_filename}")
                print(f"   Size: {width}x{height}, Mode: {mode}")
                print(f"   {original_size:,} bytes → {new_size:,} bytes ({compression_ratio:.1f}% smaller)")
                
                converted_count += 1
                
        except Exception as e:
            print(f"❌ Failed to convert {filename}: {e}")
            failed_count += 1
    
    # Summary
    print("\n" + "=" * 60)
    print(f"🎉 CONVERSION COMPLETE!")
    print(f"✅ Successfully converted: {converted_count} files")
    if failed_count > 0:
        print(f"❌ Failed conversions: {failed_count} files")
    
    # Size comparison
    if total_size_before > 0:
        total_compression = (1 - total_size_after / total_size_before) * 100
        print(f"📊 Total size: {total_size_before:,} bytes → {total_size_after:,} bytes")
        print(f"💾 Space saved: {total_compression:.1f}% ({total_size_before - total_size_after:,} bytes)")
    
    # Cleanup option
    if not keep_originals and converted_count > 0:
        response = input(f"\n🗑️  Delete original BMP files? (y/N): ").strip().lower()
        if response == 'y':
            deleted_count = 0
            for bmp_file in bmp_files:
                try:
                    os.remove(bmp_file)
                    deleted_count += 1
                except Exception as e:
                    print(f"❌ Failed to delete {bmp_file}: {e}")
            print(f"🗑️  Deleted {deleted_count} original BMP files")

def analyze_images(directory: str):
    """Analyze the converted PNG images to show their properties"""
    
    png_pattern = os.path.join(directory, "*.png")
    png_files = glob.glob(png_pattern)
    
    if not png_files:
        print(f"❌ No PNG files found in {directory}")
        return
    
    print(f"\n🔍 ANALYZING {len(png_files)} PNG FILES...")
    print("=" * 60)
    
    # Collect statistics
    dimensions = {}
    modes = {}
    total_pixels = 0
    
    for png_file in sorted(png_files[:10]):  # Show first 10 for brevity
        try:
            with Image.open(png_file) as img:
                width, height = img.size
                mode = img.mode
                filename = os.path.basename(png_file)
                
                # Count dimensions and modes
                dim_key = f"{width}x{height}"
                dimensions[dim_key] = dimensions.get(dim_key, 0) + 1
                modes[mode] = modes.get(mode, 0) + 1
                total_pixels += width * height
                
                print(f"📷 {filename}: {width}x{height}, {mode} mode, {os.path.getsize(png_file):,} bytes")
                
        except Exception as e:
            print(f"❌ Error analyzing {png_file}: {e}")
    
    if len(png_files) > 10:
        print(f"   ... and {len(png_files) - 10} more files")
    
    # Show statistics
    print(f"\n📊 IMAGE STATISTICS:")
    print(f"Common dimensions:")
    for dim, count in sorted(dimensions.items(), key=lambda x: x[1], reverse=True):
        print(f"  {dim}: {count} files")
    
    print(f"Color modes:")
    for mode, count in sorted(modes.items(), key=lambda x: x[1], reverse=True):
        print(f"  {mode}: {count} files")

def create_image_preview(directory: str, output_file: str = "preview_grid.png"):
    """Create a preview grid showing thumbnails of all images"""
    
    png_pattern = os.path.join(directory, "*.png")
    png_files = sorted(glob.glob(png_pattern))
    
    if not png_files:
        print(f"❌ No PNG files found in {directory}")
        return
    
    print(f"\n🖼️  Creating preview grid with {len(png_files)} images...")
    
    try:
        # Calculate grid size
        import math
        grid_cols = min(10, len(png_files))  # Max 10 columns
        grid_rows = math.ceil(len(png_files) / grid_cols)
        
        # Thumbnail size
        thumb_size = (128, 128)
        
        # Create grid image
        grid_width = grid_cols * thumb_size[0]
        grid_height = grid_rows * thumb_size[1]
        grid_image = Image.new('RGB', (grid_width, grid_height), (240, 240, 240))
        
        # Add thumbnails to grid
        for i, png_file in enumerate(png_files):
            try:
                with Image.open(png_file) as img:
                    # Create thumbnail
                    img.thumbnail(thumb_size, Image.Resampling.LANCZOS)
                    
                    # Calculate position
                    col = i % grid_cols
                    row = i // grid_cols
                    x = col * thumb_size[0]
                    y = row * thumb_size[1]
                    
                    # Paste thumbnail (center it if smaller than thumb_size)
                    thumb_x = x + (thumb_size[0] - img.width) // 2
                    thumb_y = y + (thumb_size[1] - img.height) // 2
                    grid_image.paste(img, (thumb_x, thumb_y))
                    
            except Exception as e:
                print(f"❌ Error adding {png_file} to preview: {e}")
        
        # Save preview grid
        preview_path = os.path.join(directory, output_file)
        grid_image.save(preview_path, "PNG", optimize=True)
        print(f"✅ Preview grid saved: {preview_path}")
        print(f"   Grid size: {grid_cols}x{grid_rows} ({grid_width}x{grid_height} pixels)")
        
    except Exception as e:
        print(f"❌ Error creating preview grid: {e}")

if __name__ == "__main__":
    input_directory = "extracted_files"
    output_directory = "extracted_files/png_converted"
    
    print("🎨 BMP to PNG Converter")
    print("=" * 60)
    
    # Convert BMP files to PNG
    convert_bmp_to_png(input_directory, output_directory, keep_originals=True)
    
    # Analyze the converted images
    analyze_images(output_directory)
    
    # Create preview grid
    create_image_preview(output_directory)
    
    print(f"\n🎉 All done! Check the '{output_directory}' folder for your PNG files!")
