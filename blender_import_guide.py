#!/usr/bin/env python3
"""
Blender Import Troubleshooting Guide for DirectX .X files
"""

import os

def main():
    print("🎯 BLENDER IMPORT TROUBLESHOOTING GUIDE")
    print("=" * 60)
    
    print("📋 ISSUE: DirectX .X files not showing up in Blender")
    print("\n🔍 POSSIBLE SOLUTIONS:")
    
    print("\n1️⃣ CHECK BLENDER VERSION & ADDONS:")
    print("   • Blender 2.8+ removed native DirectX support")
    print("   • You need to enable the DirectX addon:")
    print("     - Edit → Preferences → Add-ons")
    print("     - Search for 'DirectX'")
    print("     - Enable 'Import-Export: DirectX X Format'")
    
    print("\n2️⃣ ALTERNATIVE IMPORT METHODS:")
    print("   A) Try different import formats:")
    print("      - File → Import → Wavefront (.obj)")
    print("      - File → Import → Collada (.dae)")
    print("      - File → Import → FBX (.fbx)")
    
    print("\n   B) Check if files are actually visible:")
    print("      - After import, press 'A' to select all")
    print("      - Press 'H' to unhide objects")
    print("      - Check the Outliner panel (top right)")
    print("      - Try zooming out (scroll wheel)")
    
    print("\n3️⃣ SCALE ISSUES:")
    print("   • Models might be too small/large to see")
    print("   • After import:")
    print("     - Select the imported object")
    print("     - Press 'S' then type '10' and Enter (scale up 10x)")
    print("     - Or press 'S' then '0.1' and Enter (scale down)")
    
    print("\n4️⃣ VIEWPORT SETTINGS:")
    print("   • Change viewport shading:")
    print("     - Top right corner: try Wireframe, Solid, Material Preview")
    print("     - Press 'Z' and select different shading modes")
    
    print("\n5️⃣ MANUAL INSPECTION:")
    print("   • Check what actually imported:")
    print("     - Look in Outliner panel")
    print("     - Check if there are meshes, cameras, lights")
    print("     - Select objects and press '.' on numpad to focus")
    
    print("\n6️⃣ ALTERNATIVE CONVERTERS:")
    print("   • Try online converters:")
    print("     - https://www.aspose.com/3d/conversion/x-to-obj")
    print("     - https://products.aspose.app/3d/conversion/x-to-fbx")
    
    print("\n   • Try other software:")
    print("     - Autodesk FBX Converter")
    print("     - Open3D (Python library)")
    print("     - MeshLab")
    
    print("\n🔧 QUICK TEST STEPS:")
    print("=" * 40)
    print("1. Open Blender")
    print("2. Delete default cube (X → Delete)")
    print("3. File → Import → try different formats")
    print("4. If nothing appears:")
    print("   - Press 'A' to select all")
    print("   - Press 'Home' to frame all objects")
    print("   - Check Outliner for imported objects")
    
    print("\n📁 FILES TO TEST:")
    models_dir = "models_for_blender"
    if os.path.exists(models_dir):
        x_files = [f for f in os.listdir(models_dir) if f.lower().endswith('.x')]
        print(f"   Try these smaller files first:")
        
        # Get file sizes
        file_sizes = []
        for x_file in x_files:
            x_path = os.path.join(models_dir, x_file)
            size = os.path.getsize(x_path)
            file_sizes.append((x_file, size))
        
        # Show smallest files first (easier to debug)
        file_sizes.sort(key=lambda x: x[1])
        
        for i, (file, size) in enumerate(file_sizes[:5]):
            print(f"   🎮 {file} ({size:,} bytes)")
    
    print(f"\n💡 IF NOTHING WORKS:")
    print(f"   • The .X files might be in a proprietary format")
    print(f"   • They could be animation files rather than static models")
    print(f"   • Try extracting other KOM files (data007.kom, data009.kom)")
    
    print(f"\n🚀 NEXT STEPS:")
    print(f"   1. Try the Blender import steps above")
    print(f"   2. Let me know what happens (errors, empty scene, etc.)")
    print(f"   3. We can try extracting the other high-scoring KOM files")
    print(f"   4. Or look for a different model extraction approach")

if __name__ == "__main__":
    main()
