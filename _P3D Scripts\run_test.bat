@echo off
echo Testing P3D Mesh Extractor...
echo.

REM Try different Python commands
python test_bin_file.py
if %errorlevel% neq 0 (
    echo Trying python3...
    python3 test_bin_file.py
    if %errorlevel% neq 0 (
        echo Trying py...
        py test_bin_file.py
        if %errorlevel% neq 0 (
            echo.
            echo ERROR: Python not found!
            echo Please install Python from:
            echo https://www.python.org/downloads/
            echo or from Microsoft Store
            pause
            exit /b 1
        )
    )
)

echo.
echo Test completed! Check the output above.
pause
