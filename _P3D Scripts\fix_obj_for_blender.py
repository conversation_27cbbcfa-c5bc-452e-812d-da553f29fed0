#!/usr/bin/env python3
"""
OBJ File Cleaner and Validator for Blender Import
Fixes common issues that prevent Blender from loading OBJ files
"""

import os
import re
import math
from typing import List, Tuple, Set

def validate_vertex(vertex: Tuple[float, float, float]) -> bool:
    """Check if vertex coordinates are valid"""
    x, y, z = vertex
    
    # Check for NaN or infinity
    if any(math.isnan(coord) or math.isinf(coord) for coord in [x, y, z]):
        return False
    
    # Check for extremely large values that might cause issues
    if any(abs(coord) > 1e6 for coord in [x, y, z]):
        return False
    
    return True

def validate_face(face_indices: List[int], vertex_count: int) -> bool:
    """Check if face indices are valid"""
    
    # Face must have at least 3 vertices
    if len(face_indices) < 3:
        return False
    
    # All indices must be within valid range (1-based for OBJ)
    if any(idx < 1 or idx > vertex_count for idx in face_indices):
        return False
    
    # Face must not have duplicate vertices (degenerate)
    if len(set(face_indices)) != len(face_indices):
        return False
    
    return True

def clean_obj_file(input_path: str, output_path: str) -> dict:
    """Clean and validate OBJ file for Blender compatibility"""
    
    stats = {
        'original_vertices': 0,
        'original_faces': 0,
        'cleaned_vertices': 0,
        'cleaned_faces': 0,
        'removed_vertices': 0,
        'removed_faces': 0,
        'errors': []
    }
    
    vertices = []
    normals = []
    uvs = []
    faces = []
    
    print(f"🔧 Cleaning {os.path.basename(input_path)}...")
    
    try:
        with open(input_path, 'r') as f:
            lines = f.readlines()
        
        # Parse the file
        for line_num, line in enumerate(lines, 1):
            line = line.strip()
            
            if line.startswith('v '):  # Vertex
                try:
                    parts = line.split()
                    if len(parts) >= 4:
                        x, y, z = float(parts[1]), float(parts[2]), float(parts[3])
                        stats['original_vertices'] += 1
                        
                        if validate_vertex((x, y, z)):
                            vertices.append((x, y, z))
                        else:
                            stats['removed_vertices'] += 1
                            stats['errors'].append(f"Line {line_num}: Invalid vertex {x}, {y}, {z}")
                except (ValueError, IndexError) as e:
                    stats['errors'].append(f"Line {line_num}: Error parsing vertex: {e}")
            
            elif line.startswith('vn '):  # Normal
                try:
                    parts = line.split()
                    if len(parts) >= 4:
                        x, y, z = float(parts[1]), float(parts[2]), float(parts[3])
                        
                        # Normalize the normal vector
                        length = math.sqrt(x*x + y*y + z*z)
                        if length > 0:
                            normals.append((x/length, y/length, z/length))
                        else:
                            normals.append((0.0, 0.0, 1.0))  # Default up normal
                except (ValueError, IndexError):
                    normals.append((0.0, 0.0, 1.0))  # Default normal
            
            elif line.startswith('vt '):  # UV coordinate
                try:
                    parts = line.split()
                    if len(parts) >= 3:
                        u, v = float(parts[1]), float(parts[2])
                        # Clamp UV coordinates to valid range
                        u = max(0.0, min(1.0, u))
                        v = max(0.0, min(1.0, v))
                        uvs.append((u, v))
                except (ValueError, IndexError):
                    uvs.append((0.0, 0.0))  # Default UV
            
            elif line.startswith('f '):  # Face
                try:
                    parts = line.split()[1:]  # Skip 'f'
                    face_vertices = []
                    
                    for part in parts:
                        # Handle different face formats: v, v/vt, v/vt/vn, v//vn
                        indices = part.split('/')
                        vertex_idx = int(indices[0])
                        face_vertices.append(vertex_idx)
                    
                    stats['original_faces'] += 1
                    
                    if validate_face(face_vertices, len(vertices)):
                        faces.append(face_vertices)
                    else:
                        stats['removed_faces'] += 1
                        stats['errors'].append(f"Line {line_num}: Invalid face {face_vertices}")
                        
                except (ValueError, IndexError) as e:
                    stats['removed_faces'] += 1
                    stats['errors'].append(f"Line {line_num}: Error parsing face: {e}")
        
        stats['cleaned_vertices'] = len(vertices)
        stats['cleaned_faces'] = len(faces)
        
        # Write cleaned OBJ file
        with open(output_path, 'w') as f:
            f.write("# Cleaned OBJ file for Blender compatibility\n")
            f.write(f"# Original: {stats['original_vertices']} vertices, {stats['original_faces']} faces\n")
            f.write(f"# Cleaned: {stats['cleaned_vertices']} vertices, {stats['cleaned_faces']} faces\n\n")
            
            # Write vertices
            for v in vertices:
                f.write(f"v {v[0]:.6f} {v[1]:.6f} {v[2]:.6f}\n")
            
            f.write("\n")
            
            # Write UVs (if any)
            if uvs:
                for uv in uvs[:len(vertices)]:  # Match UV count to vertex count
                    f.write(f"vt {uv[0]:.6f} {uv[1]:.6f}\n")
                f.write("\n")
            
            # Write normals (if any)
            if normals:
                for n in normals[:len(vertices)]:  # Match normal count to vertex count
                    f.write(f"vn {n[0]:.6f} {n[1]:.6f} {n[2]:.6f}\n")
                f.write("\n")
            
            # Write faces
            f.write("g CleanedMesh\n")
            f.write("s 1\n")  # Smooth shading
            
            for face in faces:
                f.write("f")
                for idx in face:
                    if uvs and normals and idx <= len(uvs) and idx <= len(normals):
                        f.write(f" {idx}/{idx}/{idx}")
                    elif uvs and idx <= len(uvs):
                        f.write(f" {idx}/{idx}")
                    elif normals and idx <= len(normals):
                        f.write(f" {idx}//{idx}")
                    else:
                        f.write(f" {idx}")
                f.write("\n")
        
        # Print results
        if stats['cleaned_vertices'] > 0:
            print(f"  ✅ {stats['cleaned_vertices']} vertices, {stats['cleaned_faces']} faces")
            if stats['removed_vertices'] > 0 or stats['removed_faces'] > 0:
                print(f"  🧹 Removed {stats['removed_vertices']} bad vertices, {stats['removed_faces']} bad faces")
        else:
            print(f"  ❌ No valid geometry found")
        
        return stats
        
    except Exception as e:
        stats['errors'].append(f"File error: {e}")
        print(f"  ❌ Error processing file: {e}")
        return stats

def clean_all_obj_files(input_dir: str, output_dir: str):
    """Clean all OBJ files in a directory"""
    
    if not os.path.exists(input_dir):
        print(f"❌ Input directory not found: {input_dir}")
        return
    
    os.makedirs(output_dir, exist_ok=True)
    
    obj_files = [f for f in os.listdir(input_dir) if f.endswith('.obj')]
    
    if not obj_files:
        print(f"❌ No OBJ files found in {input_dir}")
        return
    
    print(f"🔧 CLEANING {len(obj_files)} OBJ FILES FOR BLENDER")
    print("=" * 60)
    
    total_stats = {
        'files_processed': 0,
        'files_with_geometry': 0,
        'total_vertices': 0,
        'total_faces': 0,
        'total_errors': 0
    }
    
    valid_files = []
    
    for obj_file in sorted(obj_files):
        input_path = os.path.join(input_dir, obj_file)
        output_path = os.path.join(output_dir, f"clean_{obj_file}")
        
        stats = clean_obj_file(input_path, output_path)
        
        total_stats['files_processed'] += 1
        total_stats['total_errors'] += len(stats['errors'])
        
        if stats['cleaned_vertices'] >= 3 and stats['cleaned_faces'] >= 1:
            total_stats['files_with_geometry'] += 1
            total_stats['total_vertices'] += stats['cleaned_vertices']
            total_stats['total_faces'] += stats['cleaned_faces']
            valid_files.append(f"clean_{obj_file}")
        else:
            # Remove empty file
            if os.path.exists(output_path):
                os.remove(output_path)
    
    # Summary
    print("\n" + "=" * 60)
    print(f"🎉 CLEANING COMPLETE!")
    print(f"📊 Files processed: {total_stats['files_processed']}")
    print(f"✅ Files with valid geometry: {total_stats['files_with_geometry']}")
    print(f"📐 Total vertices: {total_stats['total_vertices']:,}")
    print(f"🔺 Total faces: {total_stats['total_faces']:,}")
    
    if total_stats['total_errors'] > 0:
        print(f"⚠️  Total errors fixed: {total_stats['total_errors']}")
    
    if valid_files:
        print(f"\n🚀 BLENDER IMPORT INSTRUCTIONS:")
        print("1. Open Blender")
        print("2. Delete default cube (X key)")
        print("3. File → Import → Wavefront (.obj)")
        print(f"4. Navigate to '{output_dir}' folder")
        print("5. Select the clean_*.obj files")
        print("6. Click 'Import OBJ'")
        print("\n✨ These cleaned files should import without errors!")
        
        # List first few valid files
        print(f"\n📁 Valid files ready for import:")
        for i, filename in enumerate(valid_files[:10]):
            print(f"   {i+1}. {filename}")
        if len(valid_files) > 10:
            print(f"   ... and {len(valid_files) - 10} more files")
    else:
        print("\n❌ No valid 3D models could be extracted from the data.")
        print("The ROM file might contain different types of data or use a different format.")

if __name__ == "__main__":
    input_directory = "extracted_3d_models"
    output_directory = "cleaned_3d_models"
    
    print("🔧 OBJ File Cleaner for Blender")
    print("=" * 60)
    
    clean_all_obj_files(input_directory, output_directory)
