#!/usr/bin/env python3
"""
Quick PS2 Model Extractor - Fast extraction from specific ISO sections
"""

import os
import struct
import math
from typing import List, <PERSON><PERSON>

def quick_extract_ps2_models(iso_path: str, output_dir: str):
    """Quick extraction focusing on likely model locations"""
    
    print(f"⚡ QUICK PS2 MODEL EXTRACTOR")
    print("=" * 60)
    print(f"📀 ISO: {os.path.basename(iso_path)}")
    
    with open(iso_path, 'rb') as f:
        file_size = os.path.getsize(iso_path)
        print(f"📊 File size: {file_size:,} bytes ({file_size/1024/1024:.1f} MB)")
    
    os.makedirs(output_dir, exist_ok=True)
    
    # Focus on specific areas where PS2 games typically store 3D models
    search_areas = [
        (0x00000000, 0x00100000, "ISO Header Area"),           # First 1MB
        (0x00100000, 0x00500000, "Early Game Data"),          # 1-5MB
        (0x00800000, 0x01000000, "Mid Game Data"),            # 8-16MB  
        (file_size // 4, file_size // 4 + 0x00400000, "Quarter Point"),  # 1/4 through file
        (file_size // 2, file_size // 2 + 0x00400000, "Mid Point"),      # 1/2 through file
        (file_size - 0x00800000, file_size, "End Area"),      # Last 8MB
    ]
    
    model_count = 0
    
    with open(iso_path, 'rb') as f:
        for start, end, area_name in search_areas:
            if end > file_size:
                end = file_size
            
            print(f"\n🔍 Searching {area_name} (0x{start:08X} - 0x{end:08X})")
            
            f.seek(start)
            area_data = f.read(end - start)
            
            # Look for vertex patterns in this area
            models = find_models_in_area(area_data, start, area_name)
            
            for vertices, faces, offset, quality in models:
                model_count += 1
                
                print(f"   📦 Model {model_count}: {len(vertices)} vertices, {len(faces)} faces")
                print(f"      Offset: 0x{offset:08X}, Quality: {quality:.2f}")
                
                # Center and scale
                centered_vertices = center_and_scale_vertices(vertices)
                
                # Create OBJ
                obj_path = os.path.join(output_dir, f"quick_model_{model_count:03d}.obj")
                
                with open(obj_path, 'w') as obj_f:
                    obj_f.write(f"# Quick PS2 Model {model_count}\n")
                    obj_f.write(f"# Area: {area_name}\n")
                    obj_f.write(f"# Offset: 0x{offset:08X}\n")
                    obj_f.write(f"# Quality: {quality:.2f}\n")
                    obj_f.write(f"# Vertices: {len(centered_vertices)}, Faces: {len(faces)}\n\n")
                    
                    # Write vertices
                    for v in centered_vertices:
                        obj_f.write(f"v {v[0]:.6f} {v[1]:.6f} {v[2]:.6f}\n")
                    
                    obj_f.write("\n")
                    
                    # Write faces
                    obj_f.write(f"g QuickModel{model_count}\n")
                    obj_f.write("s 1\n")
                    
                    for face in faces:
                        if all(0 <= idx < len(centered_vertices) for idx in face):
                            obj_f.write(f"f {face[0]+1} {face[1]+1} {face[2]+1}\n")
                
                print(f"      💾 Saved: quick_model_{model_count:03d}.obj")
                
                if model_count >= 20:  # Limit to 20 models for quick extraction
                    break
            
            if model_count >= 20:
                break
    
    print(f"\n🎉 QUICK EXTRACTION COMPLETE!")
    print(f"✅ Found {model_count} models")
    
    if model_count > 0:
        print(f"\n🚀 BLENDER IMPORT INSTRUCTIONS:")
        print("1. Open Blender")
        print("2. Delete default cube (X key)")
        print("3. File → Import → Wavefront (.obj)")
        print("4. Navigate to 'quick_ps2_models' folder")
        print("5. Select quick_model_*.obj files")
        print("6. Click 'Import OBJ'")
        print("\n✨ These models are from the full ISO and should have proper geometry!")

def find_models_in_area(data: bytes, base_offset: int, area_name: str) -> List[Tuple[List, List, int, float]]:
    """Find 3D models in a data area"""
    
    models = []
    
    # Search for different vertex patterns
    for offset in range(0, len(data) - 1000, 256):  # Check every 256 bytes
        
        # Try float vertices
        vertices_float = extract_float_vertices(data, offset)
        if len(vertices_float) >= 20:  # At least 20 vertices
            quality = calculate_quality(vertices_float)
            if quality > 0.3:
                faces = generate_faces(len(vertices_float))
                models.append((vertices_float, faces, base_offset + offset, quality))
        
        # Try 16-bit vertices
        vertices_short = extract_short_vertices(data, offset)
        if len(vertices_short) >= 20:
            quality = calculate_quality(vertices_short)
            if quality > 0.3:
                faces = generate_faces(len(vertices_short))
                models.append((vertices_short, faces, base_offset + offset, quality))
        
        # Try to find vertex count + vertex data pattern
        vertices_counted = extract_counted_vertices(data, offset)
        if len(vertices_counted) >= 20:
            quality = calculate_quality(vertices_counted)
            if quality > 0.3:
                faces = generate_faces(len(vertices_counted))
                models.append((vertices_counted, faces, base_offset + offset, quality))
    
    # Sort by quality and return best ones
    models.sort(key=lambda x: x[3], reverse=True)
    return models[:5]  # Top 5 from each area

def extract_float_vertices(data: bytes, offset: int) -> List[Tuple[float, float, float]]:
    """Extract float vertices from data"""
    vertices = []
    
    for i in range(offset, min(offset + 2000, len(data) - 12), 12):
        try:
            x = struct.unpack('<f', data[i:i+4])[0]
            y = struct.unpack('<f', data[i+4:i+8])[0]
            z = struct.unpack('<f', data[i+8:i+12])[0]
            
            if (not any(math.isnan(v) or math.isinf(v) for v in [x, y, z]) and
                all(-500 <= v <= 500 for v in [x, y, z])):
                vertices.append((x, y, z))
            else:
                break
        except struct.error:
            break
    
    return vertices

def extract_short_vertices(data: bytes, offset: int) -> List[Tuple[float, float, float]]:
    """Extract 16-bit vertices from data"""
    vertices = []
    
    for i in range(offset, min(offset + 1000, len(data) - 6), 6):
        try:
            x_raw = struct.unpack('<H', data[i:i+2])[0]
            y_raw = struct.unpack('<H', data[i+2:i+4])[0]
            z_raw = struct.unpack('<H', data[i+4:i+6])[0]
            
            # Convert to signed and scale
            x = (x_raw - 32768) / 100.0
            y = (y_raw - 32768) / 100.0
            z = (z_raw - 32768) / 100.0
            
            if all(-300 <= v <= 300 for v in [x, y, z]):
                vertices.append((x, y, z))
            else:
                break
        except struct.error:
            break
    
    return vertices

def extract_counted_vertices(data: bytes, offset: int) -> List[Tuple[float, float, float]]:
    """Extract vertices with count header"""
    vertices = []
    
    if offset + 4 >= len(data):
        return vertices
    
    try:
        count = struct.unpack('<I', data[offset:offset+4])[0]
        
        if 10 <= count <= 5000:  # Reasonable vertex count
            for i in range(count):
                pos = offset + 4 + (i * 12)
                if pos + 12 <= len(data):
                    x = struct.unpack('<f', data[pos:pos+4])[0]
                    y = struct.unpack('<f', data[pos+4:pos+8])[0]
                    z = struct.unpack('<f', data[pos+8:pos+12])[0]
                    
                    if (not any(math.isnan(v) or math.isinf(v) for v in [x, y, z]) and
                        all(-500 <= v <= 500 for v in [x, y, z])):
                        vertices.append((x, y, z))
                    else:
                        break
    except struct.error:
        pass
    
    return vertices

def calculate_quality(vertices: List[Tuple[float, float, float]]) -> float:
    """Calculate quality score for vertices"""
    
    if len(vertices) < 3:
        return 0.0
    
    # Check diversity
    non_zero = sum(1 for v in vertices if any(abs(c) > 0.01 for c in v))
    diversity = non_zero / len(vertices)
    
    # Check coordinate ranges
    xs = [v[0] for v in vertices]
    ys = [v[1] for v in vertices]
    zs = [v[2] for v in vertices]
    
    ranges = [max(coords) - min(coords) for coords in [xs, ys, zs]]
    avg_range = sum(ranges) / 3
    
    range_score = 1.0 if 0.5 <= avg_range <= 200 else 0.5
    
    # Check for reasonable distribution
    center_x = sum(xs) / len(xs)
    center_y = sum(ys) / len(ys)
    center_z = sum(zs) / len(zs)
    
    distances = [math.sqrt((v[0]-center_x)**2 + (v[1]-center_y)**2 + (v[2]-center_z)**2) for v in vertices]
    avg_distance = sum(distances) / len(distances)
    
    dist_score = 1.0 if 1.0 <= avg_distance <= 100 else 0.5
    
    return diversity * range_score * dist_score

def generate_faces(vertex_count: int) -> List[Tuple[int, int, int]]:
    """Generate faces for vertices"""
    
    faces = []
    
    # Try triangle strip pattern first
    for i in range(vertex_count - 2):
        if i % 2 == 0:
            faces.append((i, i+1, i+2))
        else:
            faces.append((i, i+2, i+1))  # Flip winding
    
    # If that gives too many faces, use simple triangulation
    if len(faces) > vertex_count:
        faces = [(i, i+1, i+2) for i in range(0, vertex_count-2, 3)]
    
    return faces

def center_and_scale_vertices(vertices: List[Tuple[float, float, float]]) -> List[Tuple[float, float, float]]:
    """Center and scale vertices for Blender"""
    
    if not vertices:
        return vertices
    
    xs = [v[0] for v in vertices]
    ys = [v[1] for v in vertices]
    zs = [v[2] for v in vertices]
    
    # Center
    center_x = (min(xs) + max(xs)) / 2
    center_y = (min(ys) + max(ys)) / 2
    center_z = (min(zs) + max(zs)) / 2
    
    # Scale
    span_x = max(xs) - min(xs)
    span_y = max(ys) - min(ys)
    span_z = max(zs) - min(zs)
    max_span = max(span_x, span_y, span_z)
    
    scale = 4.0 / max_span if max_span > 0 else 1.0
    
    centered = []
    for x, y, z in vertices:
        new_x = (x - center_x) * scale
        new_y = (y - center_y) * scale
        new_z = (z - center_z) * scale
        centered.append((new_x, new_y, new_z))
    
    return centered

def main():
    # Look for the Shaman King ISO
    iso_file = None
    for file in os.listdir('.'):
        if 'shaman king' in file.lower() and file.lower().endswith('.iso'):
            iso_file = file
            break
    
    if not iso_file:
        print("❌ Shaman King ISO file not found!")
        return
    
    quick_extract_ps2_models(iso_file, "quick_ps2_models")

if __name__ == "__main__":
    main()
