#!/usr/bin/env python3
"""
KOM File Extractor - Extract and analyze .kom game archive files
Works with QuickBMS, .bms scripts, and EXPKDec decrypter
"""

import os
import subprocess
import shutil
from pathlib import Path

def find_extraction_tools():
    """Find QuickBMS, BMS scripts, and EXPKDec in current directory"""
    
    tools = {
        'quickbms': None,
        'bms_script': None,
        'expkdec': None,
        'kom_file': None
    }
    
    print("🔍 Scanning for extraction tools...")
    
    # Look for files in current directory
    for file in os.listdir('.'):
        file_lower = file.lower()
        
        # QuickBMS executable
        if 'quickbms' in file_lower and file_lower.endswith('.exe'):
            tools['quickbms'] = file
            print(f"   ✅ Found QuickBMS: {file}")
        
        # BMS script
        elif file_lower.endswith('.bms'):
            tools['bms_script'] = file
            print(f"   ✅ Found BMS script: {file}")
        
        # EXPKDec decrypter
        elif 'expkdec' in file_lower and file_lower.endswith('.exe'):
            tools['expkdec'] = file
            print(f"   ✅ Found EXPKDec: {file}")
        
        # KOM file
        elif file_lower.endswith('.kom'):
            tools['kom_file'] = file
            print(f"   ✅ Found KOM file: {file}")
    
    return tools

def decrypt_kom_file(kom_file, expkdec_exe, output_dir="decrypted"):
    """Decrypt KOM file using EXPKDec"""
    
    print(f"\n🔓 DECRYPTING KOM FILE")
    print("=" * 50)
    
    os.makedirs(output_dir, exist_ok=True)
    
    try:
        # Try different EXPKDec command formats
        commands_to_try = [
            [expkdec_exe, kom_file, output_dir],
            [expkdec_exe, kom_file],
            [expkdec_exe, "-d", kom_file, output_dir],
            [expkdec_exe, "-extract", kom_file, output_dir]
        ]
        
        for i, cmd in enumerate(commands_to_try):
            print(f"🔧 Trying decryption method {i+1}: {' '.join(cmd)}")
            
            try:
                result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)
                
                if result.returncode == 0:
                    print("✅ Decryption successful!")
                    
                    # Check if files were created
                    decrypted_files = []
                    if os.path.exists(output_dir):
                        decrypted_files = os.listdir(output_dir)
                    
                    # Also check current directory for output
                    for file in os.listdir('.'):
                        if file != kom_file and file.lower().endswith(('.kom', '.dat', '.bin', '.pak')):
                            if os.path.getmtime(file) > os.path.getmtime(kom_file):
                                decrypted_files.append(file)
                    
                    if decrypted_files:
                        print(f"📁 Decrypted files: {decrypted_files}")
                        return decrypted_files[0] if decrypted_files else None
                    else:
                        print("⚠️  Decryption completed but no output files found")
                
                else:
                    print(f"❌ Method {i+1} failed: {result.stderr}")
                    
            except subprocess.TimeoutExpired:
                print(f"⏰ Method {i+1} timed out")
            except Exception as e:
                print(f"❌ Method {i+1} error: {e}")
        
        print("❌ All decryption methods failed")
        return None
        
    except Exception as e:
        print(f"❌ Decryption error: {e}")
        return None

def extract_with_quickbms(target_file, quickbms_exe, bms_script, output_dir="extracted"):
    """Extract files using QuickBMS and BMS script"""
    
    print(f"\n📦 EXTRACTING WITH QUICKBMS")
    print("=" * 50)
    
    os.makedirs(output_dir, exist_ok=True)
    
    try:
        # QuickBMS command: quickbms.exe script.bms archive.kom output_folder
        cmd = [quickbms_exe, bms_script, target_file, output_dir]
        
        print(f"🔧 Running: {' '.join(cmd)}")
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            print("✅ Extraction successful!")
            
            # List extracted files
            if os.path.exists(output_dir):
                extracted_files = []
                for root, dirs, files in os.walk(output_dir):
                    for file in files:
                        rel_path = os.path.relpath(os.path.join(root, file), output_dir)
                        extracted_files.append(rel_path)
                
                print(f"📁 Extracted {len(extracted_files)} files:")
                
                # Show first 20 files
                for file in extracted_files[:20]:
                    file_path = os.path.join(output_dir, file)
                    size = os.path.getsize(file_path) if os.path.exists(file_path) else 0
                    print(f"   📄 {file} ({size:,} bytes)")
                
                if len(extracted_files) > 20:
                    print(f"   ... and {len(extracted_files) - 20} more files")
                
                return extracted_files
            else:
                print("⚠️  Extraction completed but output directory not found")
                return []
        
        else:
            print(f"❌ Extraction failed:")
            print(f"   stdout: {result.stdout}")
            print(f"   stderr: {result.stderr}")
            return []
            
    except subprocess.TimeoutExpired:
        print("⏰ Extraction timed out (>5 minutes)")
        return []
    except Exception as e:
        print(f"❌ Extraction error: {e}")
        return []

def analyze_extracted_files(extracted_dir):
    """Analyze extracted files for 3D models and textures"""
    
    print(f"\n🔍 ANALYZING EXTRACTED FILES")
    print("=" * 50)
    
    if not os.path.exists(extracted_dir):
        print(f"❌ Directory not found: {extracted_dir}")
        return
    
    file_types = {}
    model_files = []
    texture_files = []
    large_files = []
    
    for root, dirs, files in os.walk(extracted_dir):
        for file in files:
            file_path = os.path.join(root, file)
            file_size = os.path.getsize(file_path)
            file_ext = os.path.splitext(file)[1].lower()
            
            # Count file types
            if file_ext in file_types:
                file_types[file_ext] += 1
            else:
                file_types[file_ext] = 1
            
            # Look for potential model files
            model_extensions = ['.obj', '.fbx', '.dae', '.3ds', '.max', '.blend', '.x', '.md2', '.md3', '.mdl', '.mesh', '.pmx', '.pmd']
            if file_ext in model_extensions:
                model_files.append((file, file_size))
            
            # Look for texture files
            texture_extensions = ['.png', '.jpg', '.jpeg', '.bmp', '.tga', '.dds', '.tiff']
            if file_ext in texture_extensions:
                texture_files.append((file, file_size))
            
            # Track large files (might be models or archives)
            if file_size > 100000:  # >100KB
                large_files.append((file, file_size, file_ext))
    
    # Report findings
    print("📊 File type summary:")
    for ext, count in sorted(file_types.items(), key=lambda x: x[1], reverse=True):
        print(f"   {ext if ext else '(no extension)'}: {count} files")
    
    if model_files:
        print(f"\n🎮 Found {len(model_files)} potential 3D model files:")
        for file, size in sorted(model_files, key=lambda x: x[1], reverse=True)[:10]:
            print(f"   📦 {file} ({size:,} bytes)")
    
    if texture_files:
        print(f"\n🎨 Found {len(texture_files)} texture files:")
        for file, size in sorted(texture_files, key=lambda x: x[1], reverse=True)[:10]:
            print(f"   🖼️  {file} ({size:,} bytes)")
    
    if large_files:
        print(f"\n📁 Large files (might contain models):")
        for file, size, ext in sorted(large_files, key=lambda x: x[1], reverse=True)[:15]:
            print(f"   📄 {file} ({size:,} bytes) {ext}")

def main():
    print("🗃️  KOM FILE EXTRACTOR")
    print("=" * 60)
    
    # Find tools
    tools = find_extraction_tools()
    
    # Check if we have the required tools
    missing_tools = []
    if not tools['kom_file']:
        missing_tools.append("KOM file (.kom)")
    if not tools['quickbms']:
        missing_tools.append("QuickBMS executable")
    if not tools['bms_script']:
        missing_tools.append("BMS script (.bms)")
    
    if missing_tools:
        print(f"\n❌ Missing required tools:")
        for tool in missing_tools:
            print(f"   • {tool}")
        print(f"\n💡 Please ensure these files are in the current directory:")
        print(f"   • quickbms.exe (or similar)")
        print(f"   • script.bms (extraction script)")
        print(f"   • yourfile.kom (target file)")
        print(f"   • EXPKDec.exe (optional, for encrypted files)")
        return
    
    kom_file = tools['kom_file']
    target_file = kom_file
    
    # Step 1: Try decryption if EXPKDec is available
    if tools['expkdec']:
        print(f"\n🔓 EXPKDec found - attempting decryption first...")
        decrypted_file = decrypt_kom_file(kom_file, tools['expkdec'])
        if decrypted_file:
            target_file = decrypted_file
            print(f"✅ Using decrypted file: {target_file}")
        else:
            print("⚠️  Decryption failed, trying original file...")
    else:
        print(f"\n⚠️  EXPKDec not found - skipping decryption step")
    
    # Step 2: Extract with QuickBMS
    extracted_files = extract_with_quickbms(target_file, tools['quickbms'], tools['bms_script'])
    
    if extracted_files:
        # Step 3: Analyze extracted files
        analyze_extracted_files("extracted")
        
        print(f"\n🎉 EXTRACTION COMPLETE!")
        print(f"✅ Extracted {len(extracted_files)} files to 'extracted/' folder")
        print(f"\n🚀 Next steps:")
        print(f"   1. Check 'extracted/' folder for 3D models and textures")
        print(f"   2. Look for files with model extensions (.obj, .fbx, .mdl, etc.)")
        print(f"   3. Convert any proprietary formats to OBJ for Blender")
    else:
        print(f"\n❌ Extraction failed")
        print(f"💡 Try:")
        print(f"   1. Check if the BMS script is compatible with this KOM file")
        print(f"   2. Try different BMS scripts")
        print(f"   3. Check QuickBMS documentation for your specific game")

if __name__ == "__main__":
    main()
