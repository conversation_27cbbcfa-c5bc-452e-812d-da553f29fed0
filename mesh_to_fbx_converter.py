#!/usr/bin/env python3
"""
Mesh to FBX Converter - Convert .mesh files to FBX using CyberConv
"""

import os
import subprocess
import shutil
from pathlib import Path

def find_cyberconv():
    """Find CyberConv executable"""
    
    cyberconv_dir = "CyberConv"
    if os.path.exists(cyberconv_dir):
        for file in os.listdir(cyberconv_dir):
            if 'cyberconv' in file.lower() and file.lower().endswith('.exe'):
                return os.path.join(cyberconv_dir, file)
    
    return None

def find_mesh_files(directory="extracted"):
    """Find all .mesh files in extracted directory"""
    
    mesh_files = []
    
    if not os.path.exists(directory):
        return mesh_files
    
    for root, dirs, files in os.walk(directory):
        for file in files:
            if file.lower().endswith('.mesh'):
                full_path = os.path.join(root, file)
                mesh_files.append(full_path)
    
    return mesh_files

def convert_mesh_to_fbx(mesh_file, cyberconv_exe, output_dir="converted_fbx"):
    """Convert a single .mesh file to FBX using CyberConv"""
    
    print(f"\n🔄 Converting: {os.path.basename(mesh_file)}")
    
    os.makedirs(output_dir, exist_ok=True)
    
    # Create output filename
    base_name = os.path.splitext(os.path.basename(mesh_file))[0]
    output_file = os.path.join(output_dir, f"{base_name}.fbx")
    
    try:
        # Try different CyberConv command formats
        commands_to_try = [
            [cyberconv_exe, mesh_file, output_file],
            [cyberconv_exe, "-i", mesh_file, "-o", output_file],
            [cyberconv_exe, "-input", mesh_file, "-output", output_file],
            [cyberconv_exe, mesh_file, "-fbx", output_file],
            [cyberconv_exe, "-convert", mesh_file, output_file],
            [cyberconv_exe, mesh_file]  # Output to same directory
        ]
        
        for i, cmd in enumerate(commands_to_try):
            print(f"   🔧 Trying method {i+1}: {' '.join(cmd)}")
            
            try:
                result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)
                
                if result.returncode == 0:
                    print(f"   ✅ Conversion successful!")
                    
                    # Check if output file was created
                    if os.path.exists(output_file):
                        size = os.path.getsize(output_file)
                        print(f"   📄 Output: {output_file} ({size:,} bytes)")
                        return True
                    else:
                        # Check if file was created in same directory as input
                        input_dir = os.path.dirname(mesh_file)
                        potential_output = os.path.join(input_dir, f"{base_name}.fbx")
                        if os.path.exists(potential_output):
                            # Move to output directory
                            shutil.move(potential_output, output_file)
                            size = os.path.getsize(output_file)
                            print(f"   📄 Output: {output_file} ({size:,} bytes)")
                            return True
                        else:
                            print(f"   ⚠️  Command succeeded but no output file found")
                
                else:
                    print(f"   ❌ Method {i+1} failed:")
                    if result.stdout:
                        print(f"      stdout: {result.stdout[:200]}")
                    if result.stderr:
                        print(f"      stderr: {result.stderr[:200]}")
                    
            except subprocess.TimeoutExpired:
                print(f"   ⏰ Method {i+1} timed out")
            except Exception as e:
                print(f"   ❌ Method {i+1} error: {e}")
        
        print(f"   ❌ All conversion methods failed")
        return False
        
    except Exception as e:
        print(f"   ❌ Conversion error: {e}")
        return False

def analyze_mesh_files(mesh_files):
    """Analyze .mesh files to understand their format"""
    
    print(f"\n🔍 ANALYZING MESH FILES")
    print("=" * 50)
    
    for mesh_file in mesh_files[:5]:  # Analyze first 5 files
        print(f"\n📄 {os.path.basename(mesh_file)}:")
        
        try:
            size = os.path.getsize(mesh_file)
            print(f"   Size: {size:,} bytes")
            
            with open(mesh_file, 'rb') as f:
                header = f.read(64)
            
            print(f"   Header (hex): {header[:32].hex()}")
            print(f"   Header (ascii): {header[:32].decode('ascii', errors='replace')}")
            
            # Look for common mesh format signatures
            if header.startswith(b'MESH'):
                print(f"   🔍 Format: MESH format detected")
            elif header.startswith(b'FBX'):
                print(f"   🔍 Format: Already FBX format")
            elif header.startswith(b'OBJ'):
                print(f"   🔍 Format: OBJ format")
            elif b'vertex' in header.lower() or b'face' in header.lower():
                print(f"   🔍 Format: Text-based mesh format")
            else:
                print(f"   🔍 Format: Binary mesh format (unknown)")
                
        except Exception as e:
            print(f"   ❌ Analysis error: {e}")

def create_cyberconv_guide():
    """Create a guide for using CyberConv"""
    
    guide_content = """# CYBERCONV MESH CONVERSION GUIDE

## About CyberConv
CyberConv is a specialized tool for converting game mesh files to standard 3D formats like FBX.

## Common Usage Patterns:
1. `CyberConv.exe input.mesh output.fbx`
2. `CyberConv.exe -i input.mesh -o output.fbx`
3. `CyberConv.exe -convert input.mesh output.fbx`

## Supported Input Formats:
- .mesh (various game engines)
- .mdl (Source engine models)
- .smd (Source model data)
- Custom binary formats

## Output Formats:
- .fbx (Autodesk FBX)
- .obj (Wavefront OBJ)
- .dae (COLLADA)

## Tips:
- Some mesh files may require specific game-related parameters
- Check CyberConv documentation for game-specific flags
- Textures may need to be converted separately
- Some models may require skeleton/animation data

## Troubleshooting:
- If conversion fails, try different command line options
- Check if the mesh file is encrypted or compressed
- Some formats may need preprocessing
"""
    
    with open("CYBERCONV_GUIDE.md", 'w') as f:
        f.write(guide_content)
    
    print("📖 Created CYBERCONV_GUIDE.md")

def main():
    print("🔧 MESH TO FBX CONVERTER")
    print("=" * 60)
    
    # Find CyberConv
    cyberconv_exe = find_cyberconv()
    
    if not cyberconv_exe:
        print("❌ CyberConv not found in CyberConv/ directory")
        print("💡 Please ensure CyberConv.exe is in the CyberConv folder")
        return
    
    print(f"✅ Found CyberConv: {cyberconv_exe}")
    
    # Find mesh files
    mesh_files = find_mesh_files()
    
    if not mesh_files:
        print("❌ No .mesh files found in extracted/ directory")
        print("💡 Extract KOM files first using kom_extractor.py")
        return
    
    print(f"📁 Found {len(mesh_files)} .mesh files:")
    for mesh_file in mesh_files:
        size = os.path.getsize(mesh_file)
        print(f"   📄 {os.path.relpath(mesh_file)} ({size:,} bytes)")
    
    # Analyze mesh files
    analyze_mesh_files(mesh_files)
    
    # Convert mesh files
    print(f"\n🔄 CONVERTING MESH FILES TO FBX")
    print("=" * 50)
    
    success_count = 0
    
    for mesh_file in mesh_files:
        if convert_mesh_to_fbx(mesh_file, cyberconv_exe):
            success_count += 1
    
    # Create guide
    create_cyberconv_guide()
    
    print(f"\n🎉 CONVERSION COMPLETE!")
    print(f"✅ Successfully converted: {success_count}/{len(mesh_files)} files")
    
    if success_count > 0:
        output_dir = "converted_fbx"
        if os.path.exists(output_dir):
            print(f"\n📁 Converted FBX files:")
            for file in os.listdir(output_dir):
                if file.lower().endswith('.fbx'):
                    file_path = os.path.join(output_dir, file)
                    size = os.path.getsize(file_path)
                    print(f"   📦 {file} ({size:,} bytes)")
            
            print(f"\n🚀 Next steps:")
            print(f"   1. Import FBX files into Blender")
            print(f"   2. Check for textures in extracted/ directory")
            print(f"   3. Apply textures to models in Blender")
    else:
        print(f"\n💡 If conversion failed:")
        print(f"   1. Check CyberConv documentation for correct parameters")
        print(f"   2. Try manual conversion with different flags")
        print(f"   3. Some mesh formats may need game-specific tools")

if __name__ == "__main__":
    main()
