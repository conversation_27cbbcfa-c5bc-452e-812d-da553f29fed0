#!/usr/bin/env python3
"""
Deep ROM Analyzer - Exhaustive search for 3D model data in any format
"""

import os
import struct
import math
from typing import List, Tuple, Optional

def analyze_data_types(data: bytes, offset: int, length: int = 1024):
    """Analyze what types of data might be at a given offset"""
    
    if offset + length > len(data):
        length = len(data) - offset
    
    chunk = data[offset:offset + length]
    
    results = {
        'floats_le': [],
        'floats_be': [],
        'ints_le': [],
        'ints_be': [],
        'shorts_le': [],
        'shorts_be': [],
        'ascii_strings': [],
        'patterns': []
    }
    
    # Try to interpret as different data types
    for i in range(0, len(chunk) - 4, 4):
        try:
            # Little-endian float
            f_le = struct.unpack('<f', chunk[i:i+4])[0]
            if not (math.isnan(f_le) or math.isinf(f_le)) and -10000 <= f_le <= 10000:
                results['floats_le'].append((i + offset, f_le))
            
            # Big-endian float
            f_be = struct.unpack('>f', chunk[i:i+4])[0]
            if not (math.isnan(f_be) or math.isinf(f_be)) and -10000 <= f_be <= 10000:
                results['floats_be'].append((i + offset, f_be))
            
            # Little-endian int
            i_le = struct.unpack('<I', chunk[i:i+4])[0]
            if i_le < 1000000:  # Reasonable range
                results['ints_le'].append((i + offset, i_le))
            
            # Big-endian int
            i_be = struct.unpack('>I', chunk[i:i+4])[0]
            if i_be < 1000000:
                results['ints_be'].append((i + offset, i_be))
                
        except struct.error:
            pass
    
    # Try shorts
    for i in range(0, len(chunk) - 2, 2):
        try:
            s_le = struct.unpack('<H', chunk[i:i+2])[0]
            if s_le < 10000:
                results['shorts_le'].append((i + offset, s_le))
            
            s_be = struct.unpack('>H', chunk[i:i+2])[0]
            if s_be < 10000:
                results['shorts_be'].append((i + offset, s_be))
        except struct.error:
            pass
    
    # Look for ASCII strings
    try:
        text = chunk.decode('ascii', errors='ignore')
        if len(text.strip()) > 4:
            results['ascii_strings'].append(text.strip())
    except:
        pass
    
    return results

def find_potential_vertex_data(data: bytes):
    """Find potential vertex data using multiple approaches"""
    
    print("🔍 Deep scanning for vertex data patterns...")
    
    candidates = []
    
    # Scan every 1KB of the file
    for offset in range(0, len(data), 1024):
        analysis = analyze_data_types(data, offset)
        
        # Look for sequences of 3 floats (potential vertices)
        if len(analysis['floats_le']) >= 9:  # At least 3 vertices worth
            floats = [f[1] for f in analysis['floats_le']]
            
            # Group into potential vertices (every 3 floats)
            vertices = []
            for i in range(0, len(floats) - 2, 3):
                x, y, z = floats[i], floats[i+1], floats[i+2]
                
                # Check if this could be a reasonable vertex
                if all(-100 <= coord <= 100 for coord in [x, y, z]):
                    vertices.append((x, y, z))
            
            if len(vertices) >= 3:
                # Calculate quality score
                non_zero = sum(1 for v in vertices if any(abs(c) > 0.01 for c in v))
                quality = non_zero / len(vertices) if vertices else 0
                
                if quality > 0.1:  # At least 10% non-zero
                    candidates.append((offset, vertices, quality, 'float_le'))
        
        # Try big-endian floats
        if len(analysis['floats_be']) >= 9:
            floats = [f[1] for f in analysis['floats_be']]
            vertices = []
            for i in range(0, len(floats) - 2, 3):
                x, y, z = floats[i], floats[i+1], floats[i+2]
                if all(-100 <= coord <= 100 for coord in [x, y, z]):
                    vertices.append((x, y, z))
            
            if len(vertices) >= 3:
                non_zero = sum(1 for v in vertices if any(abs(c) > 0.01 for c in v))
                quality = non_zero / len(vertices) if vertices else 0
                if quality > 0.1:
                    candidates.append((offset, vertices, quality, 'float_be'))
        
        # Try interpreting shorts as fixed-point coordinates
        if len(analysis['shorts_le']) >= 9:
            shorts = [s[1] for s in analysis['shorts_le']]
            vertices = []
            for i in range(0, len(shorts) - 2, 3):
                # Convert shorts to floats (common fixed-point formats)
                x = (shorts[i] - 32768) / 1000.0  # Signed 16-bit to float
                y = (shorts[i+1] - 32768) / 1000.0
                z = (shorts[i+2] - 32768) / 1000.0
                
                if all(-50 <= coord <= 50 for coord in [x, y, z]):
                    vertices.append((x, y, z))
            
            if len(vertices) >= 3:
                non_zero = sum(1 for v in vertices if any(abs(c) > 0.01 for c in v))
                quality = non_zero / len(vertices) if vertices else 0
                if quality > 0.1:
                    candidates.append((offset, vertices, quality, 'short_le_fixed'))
    
    # Sort by quality and remove duplicates
    candidates.sort(key=lambda x: x[2], reverse=True)
    
    # Remove similar candidates
    unique_candidates = []
    for candidate in candidates:
        is_unique = True
        for existing in unique_candidates:
            if abs(candidate[0] - existing[0]) < 2048:  # Too close
                is_unique = False
                break
        if is_unique:
            unique_candidates.append(candidate)
    
    return unique_candidates[:10]

def extract_models_deep_scan(rom_path: str, output_dir: str):
    """Extract models using deep scanning approach"""
    
    print(f"🧠 DEEP ROM ANALYSIS: {os.path.basename(rom_path)}")
    print("=" * 60)
    
    with open(rom_path, 'rb') as f:
        data = f.read()
    
    print(f"📊 File size: {len(data):,} bytes")
    
    # Find potential vertex data
    candidates = find_potential_vertex_data(data)
    
    if not candidates:
        print("❌ No potential vertex data found")
        return
    
    print(f"✅ Found {len(candidates)} potential vertex data sections")
    
    os.makedirs(output_dir, exist_ok=True)
    
    for i, (offset, vertices, quality, data_type) in enumerate(candidates):
        print(f"\n📦 Model {i+1}:")
        print(f"   Offset: 0x{offset:08X} ({offset:,})")
        print(f"   Vertices: {len(vertices)}")
        print(f"   Quality: {quality:.3f}")
        print(f"   Data type: {data_type}")
        
        # Show coordinate ranges
        if vertices:
            xs = [v[0] for v in vertices]
            ys = [v[1] for v in vertices]
            zs = [v[2] for v in vertices]
            
            print(f"   X range: {min(xs):.3f} to {max(xs):.3f}")
            print(f"   Y range: {min(ys):.3f} to {max(ys):.3f}")
            print(f"   Z range: {min(zs):.3f} to {max(zs):.3f}")
        
        # Generate faces (simple triangulation)
        faces = []
        for j in range(0, len(vertices) - 2, 3):
            faces.append((j, j+1, j+2))
        
        # Also try triangle strip
        if len(vertices) >= 3:
            strip_faces = []
            for j in range(len(vertices) - 2):
                if j % 2 == 0:
                    strip_faces.append((j, j+1, j+2))
                else:
                    strip_faces.append((j, j+2, j+1))  # Flip winding
            
            # Use whichever gives more reasonable faces
            if len(strip_faces) > len(faces):
                faces = strip_faces
        
        print(f"   Faces: {len(faces)}")
        
        # Write OBJ file
        obj_path = os.path.join(output_dir, f"deep_model_{i+1:03d}.obj")
        
        with open(obj_path, 'w') as f:
            f.write(f"# Deep extracted ROM model {i+1}\n")
            f.write(f"# Source: {os.path.basename(rom_path)}\n")
            f.write(f"# Offset: 0x{offset:08X}\n")
            f.write(f"# Data type: {data_type}\n")
            f.write(f"# Quality: {quality:.3f}\n")
            f.write(f"# Vertices: {len(vertices)}, Faces: {len(faces)}\n\n")
            
            # Write vertices
            for v in vertices:
                f.write(f"v {v[0]:.6f} {v[1]:.6f} {v[2]:.6f}\n")
            
            f.write("\n")
            
            # Write faces
            f.write(f"g DeepModel{i+1}\n")
            f.write("s 1\n")
            
            for face in faces:
                f.write(f"f {face[0]+1} {face[1]+1} {face[2]+1}\n")
        
        print(f"   💾 Saved: {obj_path}")
    
    print(f"\n🎉 Deep extraction complete! {len(candidates)} models extracted.")
    print(f"\n🚀 BLENDER IMPORT INSTRUCTIONS:")
    print("1. Open Blender")
    print("2. Delete default cube (X key)")
    print("3. File → Import → Wavefront (.obj)")
    print(f"4. Navigate to '{output_dir}' folder")
    print("5. Select deep_model_*.obj files")
    print("6. Click 'Import OBJ'")
    print("\n✨ Try different models - some may show recognizable 3D shapes!")

def hex_dump_analysis(rom_path: str, start_offset: int = 0, length: int = 1024):
    """Show hex dump with analysis for manual inspection"""
    
    with open(rom_path, 'rb') as f:
        f.seek(start_offset)
        data = f.read(length)
    
    print(f"\n🔍 HEX DUMP ANALYSIS (offset 0x{start_offset:08X}):")
    print("=" * 80)
    
    for i in range(0, len(data), 16):
        chunk = data[i:i+16]
        
        # Hex representation
        hex_str = ' '.join(f'{b:02X}' for b in chunk)
        hex_str = hex_str.ljust(48)  # Pad to consistent width
        
        # ASCII representation
        ascii_str = ''.join(chr(b) if 32 <= b <= 126 else '.' for b in chunk)
        
        # Try to interpret as float
        float_str = ""
        if len(chunk) >= 4:
            try:
                f_val = struct.unpack('<f', chunk[:4])[0]
                if not (math.isnan(f_val) or math.isinf(f_val)):
                    float_str = f" [float: {f_val:.3f}]"
            except:
                pass
        
        print(f"{start_offset + i:08X}: {hex_str} |{ascii_str}|{float_str}")

if __name__ == "__main__":
    # Look for ROM file
    rom_files = [f for f in os.listdir('.') if f.lower().endswith(('.rom', '.bin', '.n64', '.z64', '.v64', '.gba', '.nds', '.3ds'))]
    
    if not rom_files:
        print("❌ No ROM file found!")
        print("💡 Please place your ROM file in this folder")
    else:
        rom_file = rom_files[0]
        print(f"🎮 Found ROM file: {rom_file}")
        
        # Run deep extraction
        extract_models_deep_scan(rom_file, "deep_extracted_models")
        
        # Show hex dump of beginning for manual analysis
        print("\n" + "="*80)
        hex_dump_analysis(rom_file, 0, 256)
