#!/usr/bin/env python3
"""
RAR File Extractor - Handle encrypted/password-protected RAR files
"""

import os
import subprocess
import sys

def find_rar_files():
    """Find RAR files in current directory"""
    rar_files = []
    for file in os.listdir('.'):
        if file.lower().endswith(('.rar', '.r00', '.r01')):
            rar_files.append(file)
    return rar_files

def try_extract_with_7zip(rar_file, password=None):
    """Try to extract using 7-Zip"""
    
    print(f"🔧 Trying 7-Zip extraction for {rar_file}")
    
    # Check if 7-Zip is available
    try:
        result = subprocess.run(['7z'], capture_output=True, text=True)
    except FileNotFoundError:
        print("❌ 7-Zip not found. Please install 7-Zip first.")
        return False
    
    # Build command
    cmd = ['7z', 'x', rar_file, '-y']  # -y = yes to all prompts
    
    if password:
        cmd.extend([f'-p{password}'])
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ Extraction successful!")
            return True
        else:
            print(f"❌ Extraction failed: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ Error running 7-Zip: {e}")
        return False

def try_common_passwords(rar_file):
    """Try common passwords for game RAR files"""
    
    common_passwords = [
        '',  # No password
        'password',
        '123',
        '1234',
        'game',
        'shaman',
        'king',
        'shamanking',
        'shaman king',
        'ps2',
        'playstation',
        'model',
        '3d',
        'extract',
        'rip',
        'dump',
        'iso',
        'bin',
        'obj',
        'blender'
    ]
    
    print(f"🔑 Trying common passwords for {rar_file}...")
    
    for password in common_passwords:
        print(f"   Trying: '{password}'")
        
        if try_extract_with_7zip(rar_file, password):
            print(f"🎉 SUCCESS! Password was: '{password}'")
            return True
    
    print("❌ None of the common passwords worked")
    return False

def manual_password_attempt(rar_file):
    """Allow user to manually enter passwords"""
    
    print(f"\n🔐 Manual password entry for {rar_file}")
    print("Enter passwords to try (press Enter with empty password to stop):")
    
    while True:
        password = input("Password: ").strip()
        
        if not password:
            break
        
        if try_extract_with_7zip(rar_file, password):
            print(f"🎉 SUCCESS! Password was: '{password}'")
            return True
        else:
            print("❌ Incorrect password, try another")
    
    return False

def check_rar_info(rar_file):
    """Get information about the RAR file"""
    
    print(f"📊 RAR File Information: {rar_file}")
    print("-" * 50)
    
    try:
        # Try to list contents without extracting
        result = subprocess.run(['7z', 'l', rar_file], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("📁 Contents:")
            lines = result.stdout.split('\n')
            for line in lines:
                if '.obj' in line.lower() or '.bin' in line.lower() or '.3d' in line.lower():
                    print(f"   🎯 {line.strip()}")
                elif 'Date' in line or 'Name' in line or '----' in line:
                    print(f"   {line.strip()}")
        else:
            print("❌ Could not list RAR contents")
            
    except Exception as e:
        print(f"❌ Error checking RAR info: {e}")

def main():
    print("🗜️  RAR FILE EXTRACTOR")
    print("=" * 50)
    
    # Find RAR files
    rar_files = find_rar_files()
    
    if not rar_files:
        print("❌ No RAR files found in current directory")
        return
    
    print(f"📁 Found RAR files: {rar_files}")
    
    for rar_file in rar_files:
        print(f"\n🎯 Processing: {rar_file}")
        
        # Check file info first
        check_rar_info(rar_file)
        
        # Try extraction methods
        success = False
        
        # 1. Try without password first
        print("\n1️⃣ Trying extraction without password...")
        if try_extract_with_7zip(rar_file):
            success = True
        
        # 2. Try common passwords
        if not success:
            print("\n2️⃣ Trying common passwords...")
            if try_common_passwords(rar_file):
                success = True
        
        # 3. Manual password entry
        if not success:
            print("\n3️⃣ Manual password entry...")
            if manual_password_attempt(rar_file):
                success = True
        
        if success:
            print(f"🎉 Successfully extracted {rar_file}!")
            
            # List extracted files
            print("\n📁 Extracted files:")
            for file in os.listdir('.'):
                if file.lower().endswith(('.obj', '.bin', '.3d', '.mdl', '.mesh')):
                    print(f"   ✅ {file}")
        else:
            print(f"❌ Could not extract {rar_file}")
            print("\n💡 Suggestions:")
            print("   • Check the source for password information")
            print("   • Try a different RAR extraction tool")
            print("   • Contact the file provider for the password")

if __name__ == "__main__":
    main()
