#!/usr/bin/env python3
"""
Organize Models - Copy all .X model files to a clean directory for Blender import
"""

import os
import shutil

def main():
    print("📦 MODEL ORGANIZER")
    print("=" * 50)
    
    extracted_dir = "extracted"
    models_dir = "models_for_blender"
    
    if not os.path.exists(extracted_dir):
        print(f"❌ Extracted directory not found: {extracted_dir}")
        return
    
    # Create models directory
    if os.path.exists(models_dir):
        shutil.rmtree(models_dir)
    os.makedirs(models_dir, exist_ok=True)
    
    # Find and copy all .X files
    x_files = []
    
    for file in os.listdir(extracted_dir):
        if file.lower().endswith('.x'):
            source_path = os.path.join(extracted_dir, file)
            dest_path = os.path.join(models_dir, file)
            
            try:
                shutil.copy2(source_path, dest_path)
                file_size = os.path.getsize(dest_path)
                x_files.append((file, file_size))
                print(f"✅ Copied: {file} ({file_size:,} bytes)")
            except Exception as e:
                print(f"❌ Failed to copy {file}: {e}")
    
    # Sort by size and display summary
    x_files.sort(key=lambda x: x[1], reverse=True)
    
    print(f"\n🎉 ORGANIZATION COMPLETE!")
    print(f"✅ Copied {len(x_files)} DirectX model files to '{models_dir}/'")
    
    print(f"\n🏆 TOP 10 LARGEST MODELS:")
    for i, (file, size) in enumerate(x_files[:10]):
        rank = "🥇" if i == 0 else "🥈" if i == 1 else "🥉" if i == 2 else f"{i+1:2d}."
        print(f"   {rank} {file:<40} ({size:,} bytes)")
    
    print(f"\n🚀 READY FOR BLENDER!")
    print(f"📁 All .X model files are in: {models_dir}/")
    print(f"\n💡 Import instructions:")
    print(f"   1. Open Blender")
    print(f"   2. File → Import → DirectX (.x)")
    print(f"   3. Navigate to {models_dir}/ folder")
    print(f"   4. Select any .X file to import")
    print(f"   5. The model will appear in your scene!")
    
    print(f"\n🎨 Character Models to Try First:")
    character_models = [f for f, s in x_files if any(keyword in f.upper() for keyword in ['BENDERS', 'WILLIAM', 'NASOD', 'SOLDIER'])]
    for model in character_models[:5]:
        print(f"   🎮 {model}")

if __name__ == "__main__":
    main()
