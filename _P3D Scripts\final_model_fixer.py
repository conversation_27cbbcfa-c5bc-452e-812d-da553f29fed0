#!/usr/bin/env python3
"""
Final Model Fixer - Center and properly scale the deep extracted models
"""

import os
import math
from typing import List, <PERSON><PERSON>

def fix_model_coordinates(input_file: str, output_file: str):
    """Fix coordinate system and scaling for a model"""
    
    vertices = []
    faces = []
    other_lines = []
    
    # Read the model
    with open(input_file, 'r') as f:
        for line in f:
            line = line.strip()
            if line.startswith('v '):
                parts = line.split()
                if len(parts) >= 4:
                    try:
                        x, y, z = float(parts[1]), float(parts[2]), float(parts[3])
                        vertices.append((x, y, z))
                    except ValueError:
                        vertices.append((0.0, 0.0, 0.0))
            elif line.startswith('f '):
                faces.append(line)
            else:
                other_lines.append(line)
    
    if not vertices:
        print(f"  ❌ No vertices found in {os.path.basename(input_file)}")
        return False
    
    print(f"🔧 Fixing {os.path.basename(input_file)}...")
    print(f"   Original vertices: {len(vertices)}")
    
    # Calculate original bounds
    xs = [v[0] for v in vertices]
    ys = [v[1] for v in vertices]
    zs = [v[2] for v in vertices]
    
    orig_min_x, orig_max_x = min(xs), max(xs)
    orig_min_y, orig_max_y = min(ys), max(ys)
    orig_min_z, orig_max_z = min(zs), max(zs)
    
    print(f"   Original bounds:")
    print(f"     X: {orig_min_x:.3f} to {orig_max_x:.3f} (span: {orig_max_x - orig_min_x:.3f})")
    print(f"     Y: {orig_min_y:.3f} to {orig_max_y:.3f} (span: {orig_max_y - orig_min_y:.3f})")
    print(f"     Z: {orig_min_z:.3f} to {orig_max_z:.3f} (span: {orig_max_z - orig_min_z:.3f})")
    
    # Center the model (move to origin)
    center_x = (orig_min_x + orig_max_x) / 2
    center_y = (orig_min_y + orig_max_y) / 2
    center_z = (orig_min_z + orig_max_z) / 2
    
    centered_vertices = []
    for x, y, z in vertices:
        centered_vertices.append((x - center_x, y - center_y, z - center_z))
    
    # Calculate spans after centering
    xs_c = [v[0] for v in centered_vertices]
    ys_c = [v[1] for v in centered_vertices]
    zs_c = [v[2] for v in centered_vertices]
    
    span_x = max(xs_c) - min(xs_c)
    span_y = max(ys_c) - min(ys_c)
    span_z = max(zs_c) - min(zs_c)
    max_span = max(span_x, span_y, span_z)
    
    # Scale to reasonable size (fit in 4x4x4 cube)
    if max_span > 0:
        scale_factor = 4.0 / max_span
    else:
        scale_factor = 1.0
    
    final_vertices = []
    for x, y, z in centered_vertices:
        final_vertices.append((x * scale_factor, y * scale_factor, z * scale_factor))
    
    # Calculate final bounds
    xs_f = [v[0] for v in final_vertices]
    ys_f = [v[1] for v in final_vertices]
    zs_f = [v[2] for v in final_vertices]
    
    print(f"   Final bounds:")
    print(f"     X: {min(xs_f):.3f} to {max(xs_f):.3f}")
    print(f"     Y: {min(ys_f):.3f} to {max(ys_f):.3f}")
    print(f"     Z: {min(zs_f):.3f} to {max(zs_f):.3f}")
    print(f"   Scale factor: {scale_factor:.3f}")
    
    # Write the fixed model
    with open(output_file, 'w') as f:
        f.write("# Final fixed ROM 3D model\n")
        f.write(f"# Centered and scaled for Blender\n")
        f.write(f"# Original file: {os.path.basename(input_file)}\n")
        f.write(f"# Scale factor: {scale_factor:.3f}\n")
        f.write(f"# Vertices: {len(final_vertices)}, Faces: {len(faces)}\n\n")
        
        # Write other header lines
        for line in other_lines:
            if line.startswith('#') or line.startswith('g ') or line.startswith('s '):
                f.write(line + '\n')
        
        f.write('\n')
        
        # Write fixed vertices
        for x, y, z in final_vertices:
            f.write(f"v {x:.6f} {y:.6f} {z:.6f}\n")
        
        f.write('\n')
        
        # Write faces
        for face_line in faces:
            f.write(face_line + '\n')
    
    print(f"   ✅ Fixed model saved to {output_file}")
    return True

def batch_fix_deep_models(input_dir: str, output_dir: str):
    """Fix all deep extracted models"""
    
    if not os.path.exists(input_dir):
        print(f"❌ Input directory not found: {input_dir}")
        return
    
    os.makedirs(output_dir, exist_ok=True)
    
    obj_files = [f for f in os.listdir(input_dir) if f.endswith('.obj')]
    
    if not obj_files:
        print(f"❌ No OBJ files found in {input_dir}")
        return
    
    print(f"🔧 FINAL MODEL FIXING")
    print("=" * 60)
    print(f"📁 Input: {input_dir}")
    print(f"📁 Output: {output_dir}")
    print(f"🎯 Processing {len(obj_files)} models")
    print("=" * 60)
    
    fixed_count = 0
    
    for obj_file in sorted(obj_files):
        input_path = os.path.join(input_dir, obj_file)
        output_path = os.path.join(output_dir, f"final_{obj_file}")
        
        if fix_model_coordinates(input_path, output_path):
            fixed_count += 1
        
        print()  # Add spacing
    
    print("=" * 60)
    print(f"🎉 FINAL FIXING COMPLETE!")
    print(f"✅ Successfully fixed: {fixed_count}/{len(obj_files)} models")
    
    if fixed_count > 0:
        print(f"\n🚀 BLENDER IMPORT INSTRUCTIONS:")
        print("1. Open Blender")
        print("2. Delete default cube (X key)")
        print("3. File → Import → Wavefront (.obj)")
        print(f"4. Navigate to '{output_dir}' folder")
        print("5. Select final_deep_model_*.obj files")
        print("6. Click 'Import OBJ'")
        print("\n✨ These models are now:")
        print("   • Centered at origin (0,0,0)")
        print("   • Properly scaled for Blender")
        print("   • Should display as recognizable 3D shapes!")
        
        print(f"\n💡 RECOMMENDED MODELS TO TRY FIRST:")
        print("   • final_deep_model_001.obj")
        print("   • final_deep_model_003.obj") 
        print("   • final_deep_model_006.obj")
        print("   • final_deep_model_007.obj")
        
        print(f"\n🎮 These are the actual 3D models from your game ROM!")
        print("   Each model represents a different game asset or object.")

def analyze_model_diversity(input_dir: str):
    """Analyze how different the models are from each other"""
    
    obj_files = [f for f in os.listdir(input_dir) if f.endswith('.obj')]
    
    print(f"\n📊 MODEL DIVERSITY ANALYSIS")
    print("=" * 40)
    
    model_stats = []
    
    for obj_file in sorted(obj_files):
        input_path = os.path.join(input_dir, obj_file)
        
        vertices = []
        with open(input_path, 'r') as f:
            for line in f:
                if line.startswith('v '):
                    parts = line.split()
                    if len(parts) >= 4:
                        try:
                            x, y, z = float(parts[1]), float(parts[2]), float(parts[3])
                            vertices.append((x, y, z))
                        except ValueError:
                            pass
        
        if vertices:
            xs = [v[0] for v in vertices]
            ys = [v[1] for v in vertices]
            zs = [v[2] for v in vertices]
            
            # Calculate bounding box volume as a measure of model size/complexity
            span_x = max(xs) - min(xs)
            span_y = max(ys) - min(ys)
            span_z = max(zs) - min(zs)
            volume = span_x * span_y * span_z
            
            model_stats.append((obj_file, len(vertices), volume, span_x, span_y, span_z))
    
    # Sort by volume (complexity)
    model_stats.sort(key=lambda x: x[2], reverse=True)
    
    print("Model                    Vertices  Volume    Dimensions (X×Y×Z)")
    print("-" * 65)
    
    for filename, vertex_count, volume, sx, sy, sz in model_stats:
        print(f"{filename:<20} {vertex_count:>8} {volume:>8.3f}  {sx:.1f}×{sy:.1f}×{sz:.1f}")
    
    print(f"\n💡 Models with higher volume are likely more complex/interesting!")

if __name__ == "__main__":
    input_directory = "deep_extracted_models"
    output_directory = "final_3d_models"
    
    print("🔧 Final Model Fixer")
    print("=" * 60)
    
    # Analyze diversity first
    if os.path.exists(input_directory):
        analyze_model_diversity(input_directory)
    
    # Fix the models
    batch_fix_deep_models(input_directory, output_directory)
