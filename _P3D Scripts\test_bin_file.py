#!/usr/bin/env python3
"""
Test script to examine the .bin file structure and test our mesh extraction code
"""

import os
import struct
from p3d_bin_to_obj import extract_mesh_data, convert_p3d_to_obj, print_mesh_info

def examine_bin_file(bin_path: str):
    """Examine the structure of a .bin file"""
    print(f"=== EXAMINING {bin_path} ===")
    
    if not os.path.exists(bin_path):
        print(f"❌ File not found: {bin_path}")
        return
    
    with open(bin_path, "rb") as f:
        data = f.read()
    
    print(f"File size: {len(data)} bytes")
    
    # Check file header
    if len(data) >= 4:
        header = data[:4]
        print(f"Header: {header} ({header.decode('ascii', errors='ignore')})")
        
        if header == b'P3D ':
            print("✅ Valid P3D file detected")
            
            # Show first 100 bytes in hex
            print("\nFirst 100 bytes (hex):")
            hex_data = data[:100].hex()
            for i in range(0, len(hex_data), 32):
                print(f"{i//2:04x}: {hex_data[i:i+32]}")
            
            # Try to find JSON start
            try:
                json_start = 12
                json_segment = data[json_start:json_start + 200]
                print(f"\nJSON segment preview:")
                print(json_segment.decode('utf-8', errors='ignore')[:200])
            except Exception as e:
                print(f"Could not preview JSON: {e}")
        else:
            print("❌ Not a P3D file")
    else:
        print("❌ File too small")
    
    print("=" * 50)

def test_mesh_extraction(bin_path: str):
    """Test the mesh extraction functionality"""
    print(f"\n=== TESTING MESH EXTRACTION ===")
    
    try:
        # Extract mesh data
        mesh_data = extract_mesh_data(bin_path)
        
        # Print detailed info
        print_mesh_info(mesh_data)
        
        # Show sample data
        if mesh_data.positions:
            print("Sample vertices (first 5):")
            for i, pos in enumerate(mesh_data.positions[:5]):
                print(f"  {i}: {pos}")
        
        if mesh_data.texcoords:
            print("\nSample UV coordinates (first 5):")
            for i, uv in enumerate(mesh_data.texcoords[:5]):
                print(f"  {i}: {uv}")
        
        if mesh_data.normals:
            print("\nSample normals (first 5):")
            for i, normal in enumerate(mesh_data.normals[:5]):
                print(f"  {i}: {normal}")
        
        if mesh_data.faces:
            print(f"\nSample faces (first 5):")
            for i, face in enumerate(mesh_data.faces[:5]):
                print(f"  {i}: {face}")
        
        return mesh_data
        
    except Exception as e:
        print(f"❌ Mesh extraction failed: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_obj_conversion(bin_path: str, obj_path: str):
    """Test the complete conversion process"""
    print(f"\n=== TESTING OBJ CONVERSION ===")
    
    try:
        mesh_data = convert_p3d_to_obj(bin_path, obj_path, verbose=True)
        
        # Verify output file was created
        if os.path.exists(obj_path):
            file_size = os.path.getsize(obj_path)
            print(f"✅ OBJ file created: {obj_path} ({file_size} bytes)")
            
            # Show first few lines of OBJ file
            with open(obj_path, 'r') as f:
                lines = f.readlines()[:20]
                print("\nFirst 20 lines of OBJ file:")
                for i, line in enumerate(lines, 1):
                    print(f"{i:2d}: {line.rstrip()}")
        else:
            print("❌ OBJ file was not created")
            
        return mesh_data
        
    except Exception as e:
        print(f"❌ OBJ conversion failed: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    bin_file = "34871HefLI.bin"
    obj_file = "test_output.obj"
    
    print("🔍 P3D Binary Mesh Extractor Test Suite")
    print("=" * 50)
    
    # Step 1: Examine the binary file
    examine_bin_file(bin_file)
    
    # Step 2: Test mesh extraction
    mesh_data = test_mesh_extraction(bin_file)
    
    # Step 3: Test OBJ conversion
    if mesh_data:
        test_obj_conversion(bin_file, obj_file)
    
    print("\n🏁 Test complete!")
