#!/usr/bin/env python3
"""
ISO 3D Model Extractor - Extract 3D models from game ISO files
"""

import os
import struct
import math
from typing import List, Tuple, Optional, Dict

def find_iso_file():
    """Find ISO file in current directory"""
    iso_extensions = ['.iso', '.bin', '.img', '.cue', '.mdf', '.nrg']
    
    for file in os.listdir('.'):
        if any(file.lower().endswith(ext) for ext in iso_extensions):
            return file
    return None

def scan_iso_for_3d_signatures(iso_path: str):
    """Scan ISO for known 3D model file signatures and patterns"""
    
    print(f"🔍 SCANNING ISO: {os.path.basename(iso_path)}")
    print("=" * 60)
    
    with open(iso_path, 'rb') as f:
        # Get file size
        f.seek(0, 2)
        file_size = f.tell()
        f.seek(0)
        
        print(f"📊 ISO size: {file_size:,} bytes ({file_size/1024/1024:.1f} MB)")
    
    # Common 3D model signatures in games
    signatures = {
        # Model formats
        b'MDLX': 'Warcraft III Model',
        b'MD2\x08': 'Quake II Model',
        b'MD3\x0F': 'Quake III Model',
        b'IDP2': 'Quake II Model Alt',
        b'IDP3': 'Quake III Model Alt',
        b'MESH': 'Generic Mesh',
        b'VERT': 'Vertex Data',
        b'FACE': 'Face Data',
        b'POLY': 'Polygon Data',
        b'GEOM': 'Geometry Data',
        
        # PlayStation formats
        b'TMD\x00': 'PlayStation TMD Model',
        b'PMD\x00': 'PlayStation PMD Model',
        b'RSD\x00': 'PlayStation RSD Model',
        
        # Nintendo formats
        b'NSBMD': 'Nintendo DS Model',
        b'NSBTX': 'Nintendo DS Texture',
        b'BMD0': 'Nintendo GameCube Model',
        b'BDL4': 'Nintendo GameCube Model',
        
        # Generic patterns
        b'\x00\x00\x00\x01\x00\x00\x00': 'Possible vertex count',
        b'vertices': 'Vertex keyword',
        b'triangles': 'Triangle keyword',
        b'faces': 'Face keyword',
    }
    
    found_signatures = []
    
    with open(iso_path, 'rb') as f:
        # Scan in chunks to handle large files
        chunk_size = 1024 * 1024  # 1MB chunks
        overlap = 64  # Overlap to catch signatures spanning chunks
        
        position = 0
        while position < file_size:
            f.seek(position)
            chunk = f.read(chunk_size + overlap)
            
            if not chunk:
                break
            
            # Search for signatures in this chunk
            for sig, desc in signatures.items():
                offset = 0
                while True:
                    pos = chunk.find(sig, offset)
                    if pos == -1:
                        break
                    
                    absolute_pos = position + pos
                    found_signatures.append((absolute_pos, sig, desc))
                    offset = pos + 1
            
            position += chunk_size
            
            # Progress indicator
            progress = (position / file_size) * 100
            print(f"\r🔍 Scanning... {progress:.1f}%", end='', flush=True)
    
    print(f"\r🔍 Scan complete!                    ")
    
    if found_signatures:
        print(f"\n✅ Found {len(found_signatures)} potential 3D data signatures:")
        
        # Group by signature type
        sig_groups = {}
        for pos, sig, desc in found_signatures:
            if desc not in sig_groups:
                sig_groups[desc] = []
            sig_groups[desc].append(pos)
        
        for desc, positions in sig_groups.items():
            print(f"   {desc}: {len(positions)} occurrences")
            if len(positions) <= 10:
                for pos in positions:
                    print(f"      0x{pos:08X} ({pos:,})")
            else:
                print(f"      First few: {[f'0x{p:08X}' for p in positions[:5]]}")
    else:
        print("❌ No known 3D model signatures found")
    
    return found_signatures

def extract_data_around_signatures(iso_path: str, signatures: List[Tuple[int, bytes, str]], output_dir: str):
    """Extract data around found signatures for analysis"""
    
    os.makedirs(output_dir, exist_ok=True)
    
    print(f"\n📦 EXTRACTING DATA AROUND SIGNATURES")
    print("=" * 60)
    
    with open(iso_path, 'rb') as f:
        for i, (pos, sig, desc) in enumerate(signatures[:20]):  # Limit to first 20
            print(f"📄 Extracting around signature {i+1}: {desc} at 0x{pos:08X}")
            
            # Extract a reasonable chunk around the signature
            extract_size = 64 * 1024  # 64KB
            start_pos = max(0, pos - extract_size // 2)
            
            f.seek(start_pos)
            data = f.read(extract_size)
            
            # Save raw data
            filename = f"sig_{i+1:03d}_{desc.replace(' ', '_').replace('/', '_')}_0x{pos:08X}.bin"
            output_path = os.path.join(output_dir, filename)
            
            with open(output_path, 'wb') as out_f:
                out_f.write(data)
            
            print(f"   💾 Saved: {filename} ({len(data):,} bytes)")

def analyze_extracted_data_for_models(data_dir: str, output_dir: str):
    """Analyze extracted data chunks for 3D model patterns"""
    
    if not os.path.exists(data_dir):
        print(f"❌ Data directory not found: {data_dir}")
        return
    
    os.makedirs(output_dir, exist_ok=True)
    
    print(f"\n🧠 ANALYZING EXTRACTED DATA FOR 3D MODELS")
    print("=" * 60)
    
    bin_files = [f for f in os.listdir(data_dir) if f.endswith('.bin')]
    
    model_count = 0
    
    for bin_file in bin_files:
        bin_path = os.path.join(data_dir, bin_file)
        
        print(f"\n🔍 Analyzing {bin_file}...")
        
        with open(bin_path, 'rb') as f:
            data = f.read()
        
        # Try to find vertex patterns in this data
        vertex_patterns = find_vertex_patterns_in_data(data)
        
        for j, (offset, vertices, quality) in enumerate(vertex_patterns[:3]):  # Max 3 per file
            if len(vertices) < 10:
                continue
                
            model_count += 1
            
            print(f"   📦 Model {model_count}: {len(vertices)} vertices, quality {quality:.2f}")
            
            # Try to find corresponding face data
            faces = find_face_data_near_vertices(data, offset, len(vertices))
            
            if not faces:
                # Generate simple faces
                faces = [(i, i+1, i+2) for i in range(0, len(vertices)-2, 3)]
            
            # Center and scale vertices
            centered_vertices = center_and_scale_vertices(vertices)
            
            # Create OBJ file
            obj_filename = f"iso_model_{model_count:03d}.obj"
            obj_path = os.path.join(output_dir, obj_filename)
            
            write_obj_file(obj_path, centered_vertices, faces, {
                'source_file': bin_file,
                'vertex_offset': offset,
                'quality': quality,
                'face_count': len(faces)
            })
            
            print(f"   💾 Created: {obj_filename}")

def find_vertex_patterns_in_data(data: bytes) -> List[Tuple[int, List[Tuple[float, float, float]], float]]:
    """Find vertex patterns in binary data"""
    
    patterns = []
    
    # Try different data interpretations
    for start_offset in range(0, min(1024, len(data)), 4):
        for stride in [12, 16, 20, 24]:  # Common vertex strides
            vertices = []
            
            for i in range(start_offset, len(data) - stride, stride):
                try:
                    # Try little-endian floats
                    x = struct.unpack('<f', data[i:i+4])[0]
                    y = struct.unpack('<f', data[i+4:i+8])[0]
                    z = struct.unpack('<f', data[i+8:i+12])[0]
                    
                    if (not any(math.isnan(v) or math.isinf(v) for v in [x, y, z]) and
                        all(-1000 <= v <= 1000 for v in [x, y, z])):
                        vertices.append((x, y, z))
                    else:
                        break
                        
                except struct.error:
                    break
            
            if len(vertices) >= 10:
                quality = calculate_vertex_quality(vertices)
                if quality > 0.2:
                    patterns.append((start_offset, vertices, quality))
        
        # Try 16-bit fixed point (like we found before)
        vertices_16 = []
        for i in range(start_offset, len(data) - 6, 6):
            try:
                x_raw = struct.unpack('<H', data[i:i+2])[0]
                y_raw = struct.unpack('<H', data[i+2:i+4])[0]
                z_raw = struct.unpack('<H', data[i+4:i+6])[0]
                
                x = (x_raw - 32768) / 1000.0
                y = (y_raw - 32768) / 1000.0
                z = (z_raw - 32768) / 1000.0
                
                if all(-100 <= v <= 100 for v in [x, y, z]):
                    vertices_16.append((x, y, z))
                else:
                    break
                    
            except struct.error:
                break
        
        if len(vertices_16) >= 10:
            quality = calculate_vertex_quality(vertices_16)
            if quality > 0.2:
                patterns.append((start_offset, vertices_16, quality))
    
    # Sort by quality and remove duplicates
    patterns.sort(key=lambda x: x[2], reverse=True)
    unique_patterns = []
    
    for pattern in patterns:
        is_unique = True
        for existing in unique_patterns:
            if abs(pattern[0] - existing[0]) < 100:
                is_unique = False
                break
        if is_unique:
            unique_patterns.append(pattern)
    
    return unique_patterns[:5]

def calculate_vertex_quality(vertices: List[Tuple[float, float, float]]) -> float:
    """Calculate quality score for vertex data"""
    
    if len(vertices) < 3:
        return 0.0
    
    # Check diversity (not all zeros)
    non_zero = sum(1 for v in vertices if any(abs(c) > 0.01 for c in v))
    diversity = non_zero / len(vertices)
    
    # Check coordinate ranges
    xs = [v[0] for v in vertices]
    ys = [v[1] for v in vertices]
    zs = [v[2] for v in vertices]
    
    ranges = [max(coords) - min(coords) for coords in [xs, ys, zs]]
    avg_range = sum(ranges) / 3
    
    range_score = 1.0 if 0.1 <= avg_range <= 100 else 0.5
    
    return diversity * range_score

def find_face_data_near_vertices(data: bytes, vertex_offset: int, vertex_count: int) -> List[Tuple[int, int, int]]:
    """Find face indices near vertex data"""
    
    faces = []
    
    # Search around the vertex data
    search_start = max(0, vertex_offset - 1000)
    search_end = min(len(data), vertex_offset + (vertex_count * 20) + 1000)
    
    for offset in range(search_start, search_end - 3, 1):
        try:
            # Try 8-bit indices
            idx1, idx2, idx3 = data[offset], data[offset+1], data[offset+2]
            
            if (0 <= idx1 < vertex_count and 
                0 <= idx2 < vertex_count and 
                0 <= idx3 < vertex_count and
                idx1 != idx2 and idx2 != idx3 and idx1 != idx3):
                faces.append((idx1, idx2, idx3))
                
                if len(faces) >= 50:  # Found enough faces
                    break
        except:
            continue
    
    return faces

def center_and_scale_vertices(vertices: List[Tuple[float, float, float]]) -> List[Tuple[float, float, float]]:
    """Center and scale vertices for Blender"""
    
    if not vertices:
        return vertices
    
    xs = [v[0] for v in vertices]
    ys = [v[1] for v in vertices]
    zs = [v[2] for v in vertices]
    
    # Center
    center_x = (min(xs) + max(xs)) / 2
    center_y = (min(ys) + max(ys)) / 2
    center_z = (min(zs) + max(zs)) / 2
    
    # Scale
    span_x = max(xs) - min(xs)
    span_y = max(ys) - min(ys)
    span_z = max(zs) - min(zs)
    max_span = max(span_x, span_y, span_z)
    
    scale = 4.0 / max_span if max_span > 0 else 1.0
    
    centered = []
    for x, y, z in vertices:
        new_x = (x - center_x) * scale
        new_y = (y - center_y) * scale
        new_z = (z - center_z) * scale
        centered.append((new_x, new_y, new_z))
    
    return centered

def write_obj_file(path: str, vertices: List[Tuple[float, float, float]], faces: List[Tuple[int, int, int]], metadata: Dict):
    """Write OBJ file with vertices and faces"""
    
    with open(path, 'w') as f:
        f.write("# 3D Model extracted from ISO file\n")
        f.write(f"# Source: {metadata.get('source_file', 'unknown')}\n")
        f.write(f"# Vertex offset: 0x{metadata.get('vertex_offset', 0):08X}\n")
        f.write(f"# Quality: {metadata.get('quality', 0):.3f}\n")
        f.write(f"# Vertices: {len(vertices)}, Faces: {metadata.get('face_count', 0)}\n\n")
        
        # Write vertices
        for v in vertices:
            f.write(f"v {v[0]:.6f} {v[1]:.6f} {v[2]:.6f}\n")
        
        f.write("\n")
        
        # Write faces
        f.write("g ISOModel\n")
        f.write("s 1\n")
        
        for face in faces:
            if all(0 <= idx < len(vertices) for idx in face):
                f.write(f"f {face[0]+1} {face[1]+1} {face[2]+1}\n")

def main():
    print("🎮 ISO 3D Model Extractor")
    print("=" * 60)
    
    # Find ISO file
    iso_file = find_iso_file()
    if not iso_file:
        print("❌ No ISO file found!")
        print("💡 Please place your game ISO file (.iso, .bin, .img) in this folder")
        return
    
    print(f"📀 Found ISO: {iso_file}")
    
    # Scan for 3D signatures
    signatures = scan_iso_for_3d_signatures(iso_file)
    
    if signatures:
        # Extract data around signatures
        extract_data_around_signatures(iso_file, signatures, "iso_extracted_data")
        
        # Analyze extracted data for models
        analyze_extracted_data_for_models("iso_extracted_data", "iso_3d_models")
        
        print(f"\n🎉 ISO EXTRACTION COMPLETE!")
        print(f"\n🚀 BLENDER IMPORT INSTRUCTIONS:")
        print("1. Open Blender")
        print("2. Delete default cube (X key)")
        print("3. File → Import → Wavefront (.obj)")
        print("4. Navigate to 'iso_3d_models' folder")
        print("5. Select iso_model_*.obj files")
        print("6. Click 'Import OBJ'")
        print("\n✨ These models should have better geometry from the complete ISO data!")
    else:
        print("\n💡 No 3D signatures found. The ISO might use a proprietary format.")
        print("   Try placing the ISO file and running again, or check if it's a supported game format.")

if __name__ == "__main__":
    main()
