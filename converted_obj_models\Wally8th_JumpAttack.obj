# Converted from KSM format using block parser
# Source: Wally8th_JumpAttack.x
# Blocks found: 15

v 0.000000 0.000000 0.000000
v 0.000000 0.000000 0.000000
v 0.000000 0.000000 0.000000
v 1.000000 1.000000 1.000000
v 1.000000 0.000000 0.000000
v 0.000000 1.000000 1.000000
v 1.000000 1.000000 1.000000
v 1.000000 0.000000 0.000000
v 0.000000 -0.000000 -0.000000
v 0.000000 0.000000 0.000000
v 0.000000 0.000000 0.000000
v 0.000000 0.000000 0.000000
v 1.000000 1.000000 1.000000
v 1.000000 0.000000 0.000000
v 0.000000 1.000000 1.000000
v 1.000000 1.000000 1.000000
v 1.000000 0.000000 0.000000
v 0.000000 -0.000000 -0.000000
v 0.000000 0.000000 0.000000
v 0.000000 0.000000 0.000000
v 0.000000 0.000000 0.000000
v 0.332031 0.109375 0.691406
v 1.000000 0.000000 0.000000
v 0.000000 1.000000 0.332031
v 0.109375 0.691406 1.000000
v 1.000000 0.000000 0.000000
v -65.000000 -65.000000 -65.000000
v -0.577350 -0.577350 -0.577350
v 1.000000 1.000000 65.000000
v -65.000000 -65.000000 0.577350
v -0.577350 -0.577350 0.000000
v 1.000000 -65.000000 -65.000000
v 65.000000 -0.577350 -0.577350
v 0.577350 1.000000 0.000000
v 65.000000 -65.000000 65.000000
v 0.577350 -0.577350 0.577350
v 0.000000 0.000000 -65.000000
v 65.000000 -65.000000 -0.577350
v 0.000000 0.000000 0.000000
v 0.000000 0.000000 0.000000
v 0.000000 0.000000 0.000000
v 480.000000 0.000000 0.000000
v 0.000000 0.000000 1120.000000
v -0.000000 0.000000 0.000000
v 0.000000 0.000000 0.000000
v -494.830048 0.000000 0.000000
v 0.000000 -0.011600 0.000000
v -0.000000 0.000000 0.000000
v 0.000000 0.000122 0.000000

f 1 2 3
f 4 5 6
f 7 8 9
f 10 11 12
f 13 14 15
f 16 17 18
f 19 20 21
f 22 23 24
f 25 26 27
f 28 29 30
f 31 32 33
f 34 35 36
f 37 38 39
f 40 41 42
f 43 44 45
f 46 47 48
