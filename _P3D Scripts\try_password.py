#!/usr/bin/env python3
"""
Try specific password for RAR file
"""

import rarfile
import os

def try_password(rar_file, password):
    """Try a specific password"""
    try:
        print(f"🔑 Trying password: '{password}'")
        
        with rarfile.RarFile(rar_file) as rf:
            rf.setpassword(password)
            rf.extractall()
            
        print(f"🎉 SUCCESS! Password was: '{password}'")
        return True
        
    except rarfile.PasswordRequired:
        print("❌ Password required")
        return False
    except rarfile.RarWrongPassword:
        print("❌ Wrong password")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def main():
    rar_file = "elsword.rar"
    
    # Try variations of the password hint "yi**la"
    password_variations = [
        "yixxla",
        "yi**la", 
        "yila",
        "yiala",
        "yibla",
        "yicla",
        "yidla",
        "yiela",
        "yifla",
        "yigla",
        "yihla",
        "yiila",
        "yijla",
        "yikla",
        "yilla",
        "yimla",
        "yinla",
        "yiola",
        "yipla",
        "yiqla",
        "yirla",
        "yisla",
        "yitla",
        "yiula",
        "yivla",
        "yiwla",
        "yixla",
        "yiyla",
        "yizla",
        "yi00la",
        "yi11la",
        "yi22la",
        "yi33la",
        "yi44la",
        "yi55la",
        "yi66la",
        "yi77la",
        "yi88la",
        "yi99la"
    ]
    
    print(f"🔐 Trying password variations for {rar_file}")
    
    for password in password_variations:
        if try_password(rar_file, password):
            print(f"\n📁 Extracted files:")
            for file in os.listdir('.'):
                if file.lower().endswith(('.pmx', '.obj', '.fbx', '.dae')):
                    print(f"   ✅ {file}")
            return
    
    print("\n❌ None of the password variations worked")
    print("💡 Try entering the exact password manually:")
    
    while True:
        manual_password = input("Enter password (or press Enter to quit): ").strip()
        if not manual_password:
            break
        
        if try_password(rar_file, manual_password):
            print(f"\n📁 Extracted files:")
            for file in os.listdir('.'):
                if file.lower().endswith(('.pmx', '.obj', '.fbx', '.dae')):
                    print(f"   ✅ {file}")
            break

if __name__ == "__main__":
    main()
