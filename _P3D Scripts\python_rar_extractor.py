#!/usr/bin/env python3
"""
Python RAR Extractor - Extract RAR files using Python libraries
"""

import os
import sys

def install_rarfile():
    """Install rarfile library if not present"""
    try:
        import rarfile
        return True
    except ImportError:
        print("📦 Installing rarfile library...")
        try:
            import subprocess
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', 'rarfile'])
            import rarfile
            return True
        except Exception as e:
            print(f"❌ Could not install rarfile: {e}")
            return False

def try_extract_rar_python(rar_file, password=None):
    """Extract RAR using Python rarfile library"""
    
    try:
        import rarfile
        
        print(f"🔧 Trying Python extraction for {rar_file}")
        
        # Open RAR file
        with rarfile.RarFile(rar_file) as rf:
            
            # List contents first
            print("📁 RAR Contents:")
            for info in rf.infolist():
                print(f"   📄 {info.filename} ({info.file_size} bytes)")
            
            # Try to extract
            if password:
                rf.setpassword(password)
            
            rf.extractall()
            print("✅ Extraction successful!")
            return True
            
    except rarfile.BadRarFile:
        print("❌ Invalid RAR file")
        return False
    except rarfile.RarWrongPassword:
        print("❌ Wrong password")
        return False
    except rarfile.PasswordRequired:
        print("❌ Password required")
        return False
    except Exception as e:
        print(f"❌ Extraction error: {e}")
        return False

def try_passwords_python(rar_file):
    """Try common passwords using Python"""
    
    passwords = [
        None,  # No password
        '',
        'password',
        '123',
        '1234',
        'elsword',
        'Elsword',
        'ELSWORD',
        'mmd',
        'MMD',
        'pmx',
        'PMX',
        'mikumikudance',
        'MikuMikuDance',
        'tda',
        'TDA',
        'immortal',
        'knight',
        'emperor',
        'rune',
        'master',
        'game',
        'model',
        '3d',
        'extract',
        'rip',
        'download',
        'free'
    ]
    
    for password in passwords:
        print(f"🔑 Trying password: '{password if password else 'no password'}'")
        
        if try_extract_rar_python(rar_file, password):
            print(f"🎉 SUCCESS! Password: '{password if password else 'no password'}'")
            return True
    
    return False

def manual_python_passwords(rar_file):
    """Manual password entry for Python extraction"""
    
    print(f"\n🔐 Manual password entry for {rar_file}")
    print("Enter passwords to try (press Enter to stop):")
    
    while True:
        password = input("Password: ").strip()
        
        if not password:
            break
        
        if try_extract_rar_python(rar_file, password):
            print(f"🎉 SUCCESS! Password: '{password}'")
            return True
    
    return False

def main():
    print("🐍 PYTHON RAR EXTRACTOR")
    print("=" * 50)
    
    # Install rarfile if needed
    if not install_rarfile():
        print("❌ Cannot proceed without rarfile library")
        print("💡 Try installing 7-Zip instead: https://www.7-zip.org/")
        return
    
    # Find RAR files
    rar_files = [f for f in os.listdir('.') if f.lower().endswith('.rar')]
    
    if not rar_files:
        print("❌ No RAR files found")
        return
    
    print(f"📁 Found RAR files: {rar_files}")
    
    for rar_file in rar_files:
        print(f"\n🎯 Processing: {rar_file}")
        
        success = False
        
        # Try common passwords
        if try_passwords_python(rar_file):
            success = True
        
        # Manual password entry
        if not success:
            if manual_python_passwords(rar_file):
                success = True
        
        if success:
            print(f"🎉 Successfully extracted {rar_file}!")
            
            # List extracted files
            print("\n📁 Extracted files:")
            for file in os.listdir('.'):
                if file.lower().endswith(('.obj', '.bin', '.3d', '.mdl', '.mesh', '.p3d')):
                    print(f"   ✅ {file}")
        else:
            print(f"❌ Could not extract {rar_file}")

if __name__ == "__main__":
    main()
