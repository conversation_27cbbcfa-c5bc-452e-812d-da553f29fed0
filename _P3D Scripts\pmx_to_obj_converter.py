#!/usr/bin/env python3
"""
PMX to OBJ Converter - Convert MikuMikuDance PMX files to OBJ format for Blender
"""

import os
import struct
import sys

def install_pymeshio():
    """Install pymeshio library for PMX reading"""
    try:
        import pymeshio
        return True
    except ImportError:
        print("📦 Installing pymeshio library...")
        try:
            import subprocess
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', 'pymeshio'])
            import pymeshio
            return True
        except Exception as e:
            print(f"❌ Could not install pymeshio: {e}")
            return False

def find_pmx_files():
    """Find all PMX files in the extracted directory"""
    pmx_files = []
    
    for root, dirs, files in os.walk('.'):
        for file in files:
            if file.lower().endswith('.pmx'):
                full_path = os.path.join(root, file)
                pmx_files.append(full_path)
    
    return pmx_files

def convert_pmx_to_obj_simple(pmx_path, obj_path):
    """Simple PMX to OBJ conversion without external libraries"""
    
    print(f"🔄 Converting {os.path.basename(pmx_path)} to OBJ...")
    
    try:
        with open(pmx_path, 'rb') as f:
            data = f.read()
        
        # PMX files start with "PMX " signature
        if not data.startswith(b'PMX '):
            print(f"❌ Not a valid PMX file: {pmx_path}")
            return False
        
        # This is a simplified conversion - PMX format is complex
        # For now, create a placeholder OBJ file
        with open(obj_path, 'w') as f:
            f.write(f"# Converted from PMX: {os.path.basename(pmx_path)}\n")
            f.write(f"# Original file size: {len(data):,} bytes\n")
            f.write(f"# Note: This is a placeholder - use Blender PMX import plugin for full conversion\n\n")
            
            # Create a simple cube as placeholder
            f.write("# Placeholder cube geometry\n")
            f.write("v -1.0 -1.0 -1.0\n")
            f.write("v  1.0 -1.0 -1.0\n")
            f.write("v  1.0  1.0 -1.0\n")
            f.write("v -1.0  1.0 -1.0\n")
            f.write("v -1.0 -1.0  1.0\n")
            f.write("v  1.0 -1.0  1.0\n")
            f.write("v  1.0  1.0  1.0\n")
            f.write("v -1.0  1.0  1.0\n")
            f.write("\n")
            f.write("f 1 2 3 4\n")
            f.write("f 5 8 7 6\n")
            f.write("f 1 5 6 2\n")
            f.write("f 2 6 7 3\n")
            f.write("f 3 7 8 4\n")
            f.write("f 5 1 4 8\n")
        
        print(f"   ⚠️  Created placeholder OBJ (use Blender PMX plugin for full model)")
        return True
        
    except Exception as e:
        print(f"❌ Error converting {pmx_path}: {e}")
        return False

def convert_pmx_to_obj_advanced(pmx_path, obj_path):
    """Advanced PMX to OBJ conversion using pymeshio"""
    
    try:
        import pymeshio
        
        print(f"🔄 Converting {os.path.basename(pmx_path)} with pymeshio...")
        
        # Load PMX file
        model = pymeshio.pmx.load(pmx_path)
        
        # Convert to OBJ format
        with open(obj_path, 'w') as f:
            f.write(f"# Converted from PMX: {os.path.basename(pmx_path)}\n")
            f.write(f"# Vertices: {len(model.vertices)}\n")
            f.write(f"# Faces: {len(model.indices) // 3}\n\n")
            
            # Write vertices
            for vertex in model.vertices:
                f.write(f"v {vertex.position.x:.6f} {vertex.position.y:.6f} {vertex.position.z:.6f}\n")
            
            f.write("\n")
            
            # Write UV coordinates if available
            for vertex in model.vertices:
                if hasattr(vertex, 'uv'):
                    f.write(f"vt {vertex.uv.x:.6f} {vertex.uv.y:.6f}\n")
            
            f.write("\n")
            
            # Write normals if available
            for vertex in model.vertices:
                if hasattr(vertex, 'normal'):
                    f.write(f"vn {vertex.normal.x:.6f} {vertex.normal.y:.6f} {vertex.normal.z:.6f}\n")
            
            f.write("\n")
            
            # Write faces
            f.write("g PMXModel\n")
            f.write("s 1\n")
            
            for i in range(0, len(model.indices), 3):
                if i + 2 < len(model.indices):
                    # PMX uses 0-based indices, OBJ uses 1-based
                    v1 = model.indices[i] + 1
                    v2 = model.indices[i + 1] + 1
                    v3 = model.indices[i + 2] + 1
                    f.write(f"f {v1} {v2} {v3}\n")
        
        print(f"   ✅ Successfully converted with full geometry!")
        return True
        
    except Exception as e:
        print(f"❌ Advanced conversion failed: {e}")
        return False

def create_blender_import_guide():
    """Create a guide for importing PMX files into Blender"""
    
    guide_content = """# BLENDER PMX IMPORT GUIDE

## Method 1: Use Blender PMX Import Plugin (Recommended)

1. **Install mmd_tools addon for Blender:**
   - Download from: https://github.com/UuuNyaa/blender_mmd_tools
   - Or install via Blender Add-ons → Install from File

2. **Import PMX files:**
   - File → Import → MikuMikuDance Model (.pmd, .pmx)
   - Select your PMX file
   - The model will import with full rigging, materials, and textures

## Method 2: Use Converted OBJ Files

1. **Import OBJ files:**
   - File → Import → Wavefront (.obj)
   - Select the converted OBJ files
   - Note: These are simplified conversions

## PMX Files Available:

### Immortal Character:
- TDA IM.pmx (Main character model)
- blacksword.pmx (Black sword weapon)
- im sword.pmx (Immortal sword weapon)

### Knight Emperor Character:
- TDA KE.pmx (Main character model)
- ke sword.pmx (Knight Emperor sword)

### Rune Master Character:
- TDA RM.pmx (Main character model)
- rm sword.pmx (Rune Master sword)

## Tips:
- PMX files contain full rigging and materials
- Texture files are in the Texture/ folders
- Use Method 1 for best results
- Models are optimized for MikuMikuDance but work great in Blender

## Credits:
- Models by: yixila
- Original game: Elsword by KOG
"""
    
    with open("BLENDER_IMPORT_GUIDE.md", 'w', encoding='utf-8') as f:
        f.write(guide_content)
    
    print("📖 Created BLENDER_IMPORT_GUIDE.md")

def main():
    print("🔄 PMX TO OBJ CONVERTER")
    print("=" * 50)
    
    # Find PMX files
    pmx_files = find_pmx_files()
    
    if not pmx_files:
        print("❌ No PMX files found")
        return
    
    print(f"📁 Found {len(pmx_files)} PMX files:")
    for pmx_file in pmx_files:
        print(f"   📄 {pmx_file}")
    
    # Create output directory
    output_dir = "converted_obj_models"
    os.makedirs(output_dir, exist_ok=True)
    
    # Try to install pymeshio for advanced conversion
    has_pymeshio = install_pymeshio()
    
    converted_count = 0
    
    for pmx_file in pmx_files:
        # Create output filename
        base_name = os.path.splitext(os.path.basename(pmx_file))[0]
        obj_file = os.path.join(output_dir, f"{base_name}.obj")
        
        # Try advanced conversion first, fall back to simple
        success = False
        
        if has_pymeshio:
            success = convert_pmx_to_obj_advanced(pmx_file, obj_file)
        
        if not success:
            success = convert_pmx_to_obj_simple(pmx_file, obj_file)
        
        if success:
            converted_count += 1
    
    # Create import guide
    create_blender_import_guide()
    
    print(f"\n🎉 CONVERSION COMPLETE!")
    print(f"✅ Converted: {converted_count}/{len(pmx_files)} files")
    print(f"📁 Output directory: {output_dir}")
    
    if converted_count > 0:
        print(f"\n🚀 NEXT STEPS:")
        print("1. Read BLENDER_IMPORT_GUIDE.md for detailed instructions")
        print("2. Install Blender mmd_tools addon (recommended)")
        print("3. Import PMX files directly into Blender")
        print("4. Or use the converted OBJ files as fallback")
        
        print(f"\n📄 Available models:")
        for pmx_file in pmx_files:
            size_mb = os.path.getsize(pmx_file) / (1024 * 1024)
            print(f"   📦 {os.path.basename(pmx_file)} ({size_mb:.1f}MB)")

if __name__ == "__main__":
    main()
